import {
  CancelledError,
  InfiniteQueryObserver,
  MutationCache,
  MutationObserver,
  QueriesObserver,
  Query,
  QueryCache,
  QueryClient,
  QueryObserver,
  VUE_QUERY_CLIENT,
  VueQueryPlugin,
  defaultShouldDehydrateMutation,
  defaultShouldDehydrateQuery,
  dehydrate,
  focusManager,
  hashQueryKey,
  hydrate,
  isCancelledError,
  isError,
  isServer,
  matchQuery,
  notifyManager,
  onlineManager,
  parseFilterArgs,
  parseMutationArgs,
  parseMutationFilterArgs,
  parseQueryArgs,
  replaceEqualDeep,
  useInfiniteQuery,
  useIsFetching,
  useIsMutating,
  useMutation,
  useQueries,
  useQuery,
  useQueryClient
} from "./chunk-MMBKGCFA.js";
import "./chunk-2LRNUAON.js";
import "./chunk-DDE2CTHX.js";
import "./chunk-RR3LOD5A.js";
export {
  CancelledError,
  InfiniteQueryObserver,
  MutationCache,
  MutationObserver,
  QueriesObserver,
  Query,
  QueryCache,
  QueryClient,
  QueryObserver,
  VUE_QUERY_CLIENT,
  VueQueryPlugin,
  defaultShouldDehydrateMutation,
  defaultShouldDehydrateQuery,
  dehydrate,
  focusManager,
  hashQueryKey,
  hydrate,
  isCancelledError,
  isError,
  isServer,
  matchQuery,
  notifyManager,
  onlineManager,
  parseFilterArgs,
  parseMutationArgs,
  parseMutationFilterArgs,
  parseQueryArgs,
  replaceEqualDeep,
  useInfiniteQuery,
  useIsFetching,
  useIsMutating,
  useMutation,
  useQueries,
  useQuery,
  useQueryClient
};
