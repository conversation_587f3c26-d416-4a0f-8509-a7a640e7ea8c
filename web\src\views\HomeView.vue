<script setup lang="ts">
import Button from '@/components/ui/button/Button.vue';
import { useLogout } from '@/mutations/auth';
import { useAuthStore } from '@/stores/auth';
import { computed } from 'vue';
import { RouterLink } from 'vue-router';

const authStore = useAuthStore();
const isAuthenticated = computed(() => authStore.getIsAuthenticated());
const logout = useLogout();

</script>

<template>
  <main class="w-full h-screen grid place-content-center place-items-center">
    <div class="w-full h-full flex flex-col gap-12">
      <h1 class="text-7xl font-bold font-rocker text-secondary">Reforged</h1>
      <div class="max-w-sm flex items-center justify-center gap-4">
        <RouterLink v-if="!isAuthenticated" to="/login" class="text-2xl font-medium font-rocknroll text-primary">Login</RouterLink>
        <RouterLink v-if="!isAuthenticated" to="/register" class="text-2xl font-medium font-rocknroll text-primary">Register</RouterLink>
        <RouterLink v-if="isAuthenticated" to="/game" class="text-2xl font-medium font-rocknroll text-primary">Game</RouterLink>
        <RouterLink v-if="isAuthenticated" to="/settings" class="text-2xl font-medium font-rocknroll text-primary">Settings</RouterLink>
        <Button variant="ghost" v-if="isAuthenticated" @click="logout.mutate()" class="text-2xl hover:bg-transparent p-0 font-medium font-rocknroll text-primary">Logout</Button>
      </div>
    </div>
  </main>
</template>
