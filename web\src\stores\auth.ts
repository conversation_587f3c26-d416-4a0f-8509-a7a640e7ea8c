import { defineStore } from 'pinia'
import { ref } from 'vue'
import { z } from 'zod'

const Profile = z.object({
  id: z.string(),
  pid: z.number(),
  user_id: z.string(),
  country: z.string(),
  age: z.number(),
  gender: z.string(),
  avatar: z.string(),
})

const Info = z.object({
  id: z.string(),
  pid: z.number(),
  username: z.string(),
  role: z.string(),
  profile: Profile,
})

const User = z.object({
  user: Info,
  access_token: z.string(),
  refresh_token: z.string(),
  access_token_expiration: z.string(),
  refresh_token_expiration: z.string(),
})

export type AuthData = z.infer<typeof User>
export type UserInfoData = z.infer<typeof Info>
export type InfoData = z.infer<typeof Info>
export type ProfileData = z.infer<typeof Profile>

export const useAuthStore = defineStore('auth', {
  state: () => {
    const authDataVal = localStorage.getItem('reforged-auth-data')

    if (!authDataVal)
      return {
        authData: ref<AuthData | null>(null),
        isAuthenticated: ref(false),
      }

    const { data, error } = User.safeParse(JSON.parse(authDataVal))

    if (error) {
      console.error('Error parsing auth data:', error)
      return {
        authData: ref<AuthData | null>(null),
        isAuthenticated: ref(false),
      }
    }

    return {
      authData: ref<AuthData | null>(data),
      isAuthenticated: ref(true),
    }
  },
  actions: {
    getIsAuthenticated() {
      return this.authData !== null
    },
    setUserData(userData: AuthData) {
      this.authData = userData
      localStorage.setItem('reforged-auth-data', JSON.stringify(userData))
      this.isAuthenticated = true
    },
    removeUserData() {
      this.authData = null
      this.isAuthenticated = false
      localStorage.removeItem('reforged-auth-data')
    },
  },
})
