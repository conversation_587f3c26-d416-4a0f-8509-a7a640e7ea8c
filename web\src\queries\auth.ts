import { useQuery } from '@tanstack/vue-query'
import { API_URL } from '@/lib/config'
import { type } from 'arktype'

export const VerifyAccessTokenResponse = type({
  is_valid: 'boolean',
})

export type VerifyAccessTokenResponse = typeof VerifyAccessTokenResponse.infer

async function verifyAccessToken(token: string): Promise<VerifyAccessTokenResponse> {
  try {
    const response = await fetch(`${API_URL}/api/v1/auth/validate/${token}`)

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    return await response.json()
  } catch (error) {
    console.error('Error verifying access token:', error)
    return { is_valid: false }
  }
}

export function useVerifyAccessToken(token: string) {
  return useQuery({
    queryKey: ['verifyAccessToken', token],
    queryFn: () => verifyAccessToken(token),
    enabled: !!token,
  })
}
