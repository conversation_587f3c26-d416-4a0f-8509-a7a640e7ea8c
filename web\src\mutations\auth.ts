import { API_URL } from '@/lib/config'
import { handleApiError, type ApiResponse } from '@/lib/utils'
import { useAuthStore, type AuthData, type UserInfoData } from '@/stores/auth'
import { useMutation } from '@tanstack/vue-query'
import { z } from 'zod'

export const loginSchema = z.object({
  username: z.string({ error: 'Please enter your username' }).max(255, 'Your username is too long'),
  password: z.string({ error: 'Please enter your password' }).max(255, 'Your password is too long'),
  captcha: z.string({ error: 'Captcha is required' }).min(5, 'Captcha is required'),
})

export interface Error {
  [key: string]: ErrorInfo
}

export interface ErrorInfo {
  code: string
  message: any
  params: Params
}

export interface Params {
  min: number
  value: string
  max: number
}

export interface LoginResponse {
  user?: UserInfoData
  access_token?: string
  refresh_token?: string
  access_token_expiration?: string
  refresh_token_expiration?: string
  error?: string
}

export type LoginDTO = z.infer<typeof loginSchema>

async function login(data: LoginDTO): Promise<LoginResponse> {
  const response = await fetch(`${API_URL}/api/v1/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  const json = (await response.json()) as LoginResponse

  if (json.error) {
    return handleApiError(json)
  }

  return json
}

export function useLogin() {
  return useMutation({
    mutationKey: ['login'],
    mutationFn: (data: LoginDTO) => login(data),
    onSuccess: (data) => {
      const {
        error,
        user,
        access_token,
        refresh_token,
        access_token_expiration,
        refresh_token_expiration,
      } = data

      if (error) {
        console.error('Error logging in:', error)
        return error
      }

      if (!user) {
        return
      }

      const authData: AuthData = {
        user,
        access_token: access_token ?? '',
        refresh_token: refresh_token ?? '',
        access_token_expiration: access_token_expiration ?? '',
        refresh_token_expiration: refresh_token_expiration ?? '',
      }

      useAuthStore().setUserData(authData)
    },
    onError: (error) => {
      console.error('Error logging in:', error)
      return error
    },
  })
}

export const registerSchema = z.object({
  username: z.string({ error: 'Please enter your username' }).max(255, 'Your username is too long'),
  email: z.email({ error: 'Please enter your email' }).max(255, 'Your email is too long'),
  password: z.string({ error: 'Please enter your password' }).max(255, 'Your password is too long'),
  gender: z.enum(['M', 'F'], { error: 'Please select your gender' }),
  captcha: z.string({ error: 'Captcha is required' }).min(5, 'Captcha is required'),
})

export type RegisterDTO = z.infer<typeof registerSchema>

export interface RegisterResponse extends ApiResponse {
  message?: string
}

async function register(data: RegisterDTO): Promise<RegisterResponse> {
  const response = await fetch(`${API_URL}/api/v1/users`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  const json = (await response.json()) as RegisterResponse

  if (json.error) {
    return handleApiError(json)
  }

  return json
}

export function useRegister() {
  return useMutation({
    mutationKey: ['register'],
    mutationFn: (data: RegisterDTO) => register(data),
    onSuccess: () => {
      console.log('Register successful')
    },
    onError: (error) => {
      console.error('Error registering:', error)
      return error
    },
  })
}

async function logout(): Promise<void> {
  const response = await fetch(`${API_URL}/api/v1/auth/logout`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({ refresh_token: null }),
  })

  if (!response.ok) {
    throw new Error('Network response was not ok')
  }

  return await response.json()
}

export function useLogout() {
  return useMutation({
    mutationKey: ['logout'],
    mutationFn: () => logout(),
    onSuccess: () => {
      useAuthStore().removeUserData()
    },
    onError: (error) => {
      console.error('Error logging out:', error)
      useAuthStore().removeUserData()
      return error
    },
  })
}
