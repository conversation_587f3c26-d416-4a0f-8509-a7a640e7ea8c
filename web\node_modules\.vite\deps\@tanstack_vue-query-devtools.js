import {
  createComponent,
  createSignal,
  lazy,
  mergeProps,
  render,
  setupStyleSheet
} from "./chunk-ADFUSSGJ.js";
import {
  onlineManager,
  useQueryClient
} from "./chunk-MMBKGCFA.js";
import "./chunk-2LRNUAON.js";
import {
  createElementBlock,
  defineComponent,
  onMounted,
  onScopeDispose,
  openBlock,
  ref,
  watchEffect
} from "./chunk-DDE2CTHX.js";
import {
  __privateAdd,
  __privateGet,
  __privateSet
} from "./chunk-RR3LOD5A.js";

// node_modules/.pnpm/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/dev.js
var _client, _onlineManager, _queryFlavor, _version, _isMounted, _styleNonce, _shadowDOMTarget, _buttonPosition, _position, _initialIsOpen, _errorTypes, _hideDisabledQueries, _Component, _dispose, _a;
var TanstackQueryDevtools = (_a = class {
  constructor(config) {
    __privateAdd(this, _client);
    __privateAdd(this, _onlineManager);
    __privateAdd(this, _queryFlavor);
    __privateAdd(this, _version);
    __privateAdd(this, _isMounted, false);
    __privateAdd(this, _styleNonce);
    __privateAdd(this, _shadowDOMTarget);
    __privateAdd(this, _buttonPosition);
    __privateAdd(this, _position);
    __privateAdd(this, _initialIsOpen);
    __privateAdd(this, _errorTypes);
    __privateAdd(this, _hideDisabledQueries);
    __privateAdd(this, _Component);
    __privateAdd(this, _dispose);
    const {
      client,
      queryFlavor,
      version,
      onlineManager: onlineManager2,
      buttonPosition,
      position,
      initialIsOpen,
      errorTypes,
      styleNonce,
      shadowDOMTarget,
      hideDisabledQueries
    } = config;
    __privateSet(this, _client, createSignal(client));
    __privateSet(this, _queryFlavor, queryFlavor);
    __privateSet(this, _version, version);
    __privateSet(this, _onlineManager, onlineManager2);
    __privateSet(this, _styleNonce, styleNonce);
    __privateSet(this, _shadowDOMTarget, shadowDOMTarget);
    __privateSet(this, _buttonPosition, createSignal(buttonPosition));
    __privateSet(this, _position, createSignal(position));
    __privateSet(this, _initialIsOpen, createSignal(initialIsOpen));
    __privateSet(this, _errorTypes, createSignal(errorTypes));
    __privateSet(this, _hideDisabledQueries, createSignal(hideDisabledQueries));
  }
  setButtonPosition(position) {
    __privateGet(this, _buttonPosition)[1](position);
  }
  setPosition(position) {
    __privateGet(this, _position)[1](position);
  }
  setInitialIsOpen(isOpen) {
    __privateGet(this, _initialIsOpen)[1](isOpen);
  }
  setErrorTypes(errorTypes) {
    __privateGet(this, _errorTypes)[1](errorTypes);
  }
  setClient(client) {
    __privateGet(this, _client)[1](client);
  }
  mount(el) {
    if (__privateGet(this, _isMounted)) {
      throw new Error("Devtools is already mounted");
    }
    const dispose = render(() => {
      const _self$ = this;
      const [btnPosition] = __privateGet(this, _buttonPosition);
      const [pos] = __privateGet(this, _position);
      const [isOpen] = __privateGet(this, _initialIsOpen);
      const [errors] = __privateGet(this, _errorTypes);
      const [hideDisabledQueries] = __privateGet(this, _hideDisabledQueries);
      const [queryClient] = __privateGet(this, _client);
      let Devtools;
      if (__privateGet(this, _Component)) {
        Devtools = __privateGet(this, _Component);
      } else {
        Devtools = lazy(() => import("./EDEL3XIZ-NJROQV64.js"));
        __privateSet(this, _Component, Devtools);
      }
      setupStyleSheet(__privateGet(this, _styleNonce), __privateGet(this, _shadowDOMTarget));
      return createComponent(Devtools, mergeProps({
        get queryFlavor() {
          return __privateGet(_self$, _queryFlavor);
        },
        get version() {
          return __privateGet(_self$, _version);
        },
        get onlineManager() {
          return __privateGet(_self$, _onlineManager);
        },
        get shadowDOMTarget() {
          return __privateGet(_self$, _shadowDOMTarget);
        }
      }, {
        get client() {
          return queryClient();
        },
        get buttonPosition() {
          return btnPosition();
        },
        get position() {
          return pos();
        },
        get initialIsOpen() {
          return isOpen();
        },
        get errorTypes() {
          return errors();
        },
        get hideDisabledQueries() {
          return hideDisabledQueries();
        }
      }));
    }, el);
    __privateSet(this, _isMounted, true);
    __privateSet(this, _dispose, dispose);
  }
  unmount() {
    var _a2;
    if (!__privateGet(this, _isMounted)) {
      throw new Error("Devtools is not mounted");
    }
    (_a2 = __privateGet(this, _dispose)) == null ? void 0 : _a2.call(this);
    __privateSet(this, _isMounted, false);
  }
}, _client = new WeakMap(), _onlineManager = new WeakMap(), _queryFlavor = new WeakMap(), _version = new WeakMap(), _isMounted = new WeakMap(), _styleNonce = new WeakMap(), _shadowDOMTarget = new WeakMap(), _buttonPosition = new WeakMap(), _position = new WeakMap(), _initialIsOpen = new WeakMap(), _errorTypes = new WeakMap(), _hideDisabledQueries = new WeakMap(), _Component = new WeakMap(), _dispose = new WeakMap(), _a);

// node_modules/.pnpm/@tanstack+vue-query-devtool_2369d3166eebd0e109a28687372f03f2/node_modules/@tanstack/vue-query-devtools/dist/esm/devtools.vue.js
var _sfc_main = defineComponent({
  __name: "devtools",
  props: {
    initialIsOpen: { type: Boolean },
    buttonPosition: {},
    position: {},
    client: {},
    errorTypes: {},
    styleNonce: {},
    shadowDOMTarget: {},
    hideDisabledQueries: { type: Boolean }
  },
  setup(__props) {
    const props = __props;
    const div = ref();
    const client = props.client || useQueryClient();
    const devtools = new TanstackQueryDevtools({
      client,
      queryFlavor: "Vue Query",
      version: "5",
      onlineManager,
      buttonPosition: props.buttonPosition,
      position: props.position,
      initialIsOpen: props.initialIsOpen,
      errorTypes: props.errorTypes,
      styleNonce: props.styleNonce,
      shadowDOMTarget: props.shadowDOMTarget,
      hideDisabledQueries: props.hideDisabledQueries
    });
    watchEffect(() => {
      devtools.setButtonPosition(props.buttonPosition || "bottom-right");
      devtools.setPosition(props.position || "bottom");
      devtools.setInitialIsOpen(props.initialIsOpen);
      devtools.setErrorTypes(props.errorTypes || []);
    });
    onMounted(() => {
      devtools.mount(div.value);
    });
    onScopeDispose(() => {
      devtools.unmount();
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        className: "tsqd-parent-container",
        ref_key: "div",
        ref: div
      }, null, 512);
    };
  }
});

// node_modules/.pnpm/@tanstack+vue-query-devtool_2369d3166eebd0e109a28687372f03f2/node_modules/@tanstack/vue-query-devtools/dist/esm/index.js
var VueQueryDevtools = false ? function() {
  return null;
} : _sfc_main;
export {
  VueQueryDevtools
};
//# sourceMappingURL=@tanstack_vue-query-devtools.js.map
