<script setup lang="ts">
import { Button } from '@/components/ui/button'
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
} from '@/components/ui/form'
import { loginSchema, useLogin } from '@/mutations/auth'
import { toTypedSchema } from '@vee-validate/zod'
import { RouterLink, useRouter } from 'vue-router'
import { ArrowRight, Loader } from 'lucide-vue-next'
import { effect } from 'vue'
import { useToast } from '@/components/ui/toast'

const {toast} = useToast();

const typedLoginSchema = toTypedSchema(loginSchema)

const loginMutation = useLogin();
const router = useRouter()

const onSubmit = (values: any) => {
    loginMutation.mutate(values);
}

effect(function showToastOnSuccess() {
    if (loginMutation.data.value?.user) {
        toast({
            title: 'Login successful',
            description: 'You have been logged in successfully, redirecting...',
            duration: 2000,
        })

        const timeout = setTimeout(() => {
            router.push('/')
        }, 2000)

        return () => clearTimeout(timeout)
    }
})

</script>

<template>
  <div class="w-full h-full min-h-3xl flex bg-secondary p-6 rounded-4 max-w-7xl">
    <!-- Left side - Orange gradient with logo and back button -->
    <div class="flex-1 bg-gradient-to-br bg-primary p-8 flex flex-col relative rounded-2">
      <!-- Logo -->
      <div class="flex items-center mb-8">
        <div class="w-24 h-24 rounded-full flex items-center justify-center relative">
          <img src="/favicon.svg" alt="Logo" />
        </div>
      </div>

      <div class="absolute top-4 right-4">
        <Button
          variant="outline"
          class="bg-background/63 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm"
          as-child
        >
          <RouterLink to="/" class="!text-secondary flex items-center justify-center gap-2 !rounded-4">
            Back to website
            <ArrowRight class="w-12 h-12" />
          </RouterLink>
        </Button>
      </div>
    </div>

    <div class="flex-1 bg-secondary p-8 flex items-center justify-center">
      <div class="w-full max-w-md space-y-6">
        <!-- Welcome heading -->
        <div class="text-left space-y-2">
          <h1 class="text-white text-4xl font-medium">Welcome back!</h1>
          <p class="text-gray-300">
            Don't have an account?
              <RouterLink
                to="/register"
                class="text-orange-400 hover:text-orange-300 underline-offset-4 hover:underline"
              >
                Create now!
            </RouterLink>
          </p>
        </div>

        <Form @submit="onSubmit" :validation-schema="typedLoginSchema" class="space-y-6">
          <FormField v-slot="{ componentField }" name="username">
            <FormItem>
              <FormLabel class="text-white text-sm font-medium">Username</FormLabel>
              <FormControl>
                <input
                  v-bind="componentField"
                  type="text"
                  placeholder="Username"
                  class="w-full px-3 py-2 bg-white rounded-md border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent transition-colors"
                />
              </FormControl>
              <p class="text-sm text-gray-400">Enter your desired username</p>
              <FormMessage class="text-red-400 text-sm" />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="password">
            <FormItem>
              <FormLabel class="text-white text-sm font-medium">Password</FormLabel>
              <FormControl>
                <input
                  v-bind="componentField"
                  type="password"
                  placeholder="Password"
                  class="w-full px-3 py-2 bg-white rounded-md border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent transition-colors"
                />
              </FormControl>
              <p class="text-sm text-gray-400">Enter your password</p>
              <FormMessage class="text-red-400 text-sm" />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="captcha">
            <FormItem>
              <FormLabel class="text-white text-sm font-medium">Captcha</FormLabel>
              <FormControl>
                <input
                  v-bind="componentField"
                  type="text"
                  placeholder="Captcha"
                  class="w-full px-3 py-2 bg-white rounded-md border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent transition-colors"
                />
              </FormControl>
              <FormMessage class="text-red-400 text-sm" />
            </FormItem>
          </FormField>

          <div v-if="loginMutation.data.value?.error" class="text-red-500">
            {{ loginMutation.data.value?.error ||"" }}
          </div>

          <Button
            type="submit"
            size="lg"
            variant="default"
            class="w-full !bg-primary !hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-md transition-colors"
            :disabled="loginMutation.isPending.value"
          >
            Login
            <Loader v-if="loginMutation.isPending.value" class="w-12 h-12 animate-spin" />
          </Button>
        </Form>
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>