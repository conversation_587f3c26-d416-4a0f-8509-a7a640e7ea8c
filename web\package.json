{"name": "web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@fontsource/new-rocker": "^5.2.6", "@fontsource/poppins": "^5.2.6", "@fontsource/rocknroll-one": "^5.2.6", "@tanstack/vue-query": "^4.40.0", "@tanstack/vue-query-devtools": "^5.84.0", "@unocss/reset": "^66.3.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.6.0", "arktype": "^2.1.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "core-js": "^3.44.0", "lucide-vue-next": "^0.525.0", "pinia": "^3.0.3", "radix-vue": "^1.9.17", "reka-ui": "^2.3.2", "tailwind-merge": "^3.3.1", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "zod": "^4.0.14"}, "devDependencies": {"@playwright/test": "^1.53.1", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@unocss/preset-wind3": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "prettier": "3.5.3", "typescript": "~5.8.0", "unocss": "^66.3.3", "unocss-preset-animations": "^1.2.1", "unocss-preset-shadcn": "^0.5.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}, "packageManager": "pnpm@10.13.1"}