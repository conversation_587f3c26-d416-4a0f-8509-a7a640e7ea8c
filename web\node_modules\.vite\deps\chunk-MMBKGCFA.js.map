{"version": 3, "sources": ["../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/subscribable.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/utils.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/focusManager.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/onlineManager.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/retryer.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/logger.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/notifyManager.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/removable.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/query.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/queryCache.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/mutation.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/mutationCache.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/infiniteQueryBehavior.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/queryClient.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/queryObserver.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/queriesObserver.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/infiniteQueryObserver.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/mutationObserver.ts", "../../.pnpm/@tanstack+query-core@4.40.0/node_modules/@tanstack/query-core/src/hydration.ts", "../../.pnpm/vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules/vue-demi/lib/index.mjs", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/utils.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/useQueryClient.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/queryCache.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/mutationCache.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/queryClient.ts", "../../.pnpm/@tanstack+match-sorter-utils@8.19.4/node_modules/@tanstack/match-sorter-utils/src/remove-accents.ts", "../../.pnpm/@tanstack+match-sorter-utils@8.19.4/node_modules/@tanstack/match-sorter-utils/src/index.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/devtools/utils.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/devtools/devtools.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/vueQueryPlugin.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/useBaseQuery.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/useQuery.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/useQueries.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/useInfiniteQuery.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/useMutation.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/useIsFetching.ts", "../../.pnpm/@tanstack+vue-query@4.40.0_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-query/src/useIsMutating.ts"], "sourcesContent": ["type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: Set<{ listener: TListener }>\n\n  constructor() {\n    this.listeners = new Set()\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    const identity = { listener }\n    this.listeners.add(identity)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(identity)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n", "import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport type {\n  FetchStatus,\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in window\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>,\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1) ? [{ ...arg2, queryKey: arg1 }, arg3] : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs<\n  TFilters extends MutationFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1)\n      ? [{ ...arg2, mutationKey: arg1 }, arg3]\n      : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (\n    typeof fetchStatus !== 'undefined' &&\n    fetchStatus !== query.state.fetchStatus\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(a, b)\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some((key) => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: unknown): value is QueryKey {\n  return Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void) {\n  sleep(0).then(callback)\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n  return\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual?.(prevData, data)) {\n    return prevData as TData\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data)\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable {\n  private focused?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibillitychange and focus\n        window.addEventListener('visibilitychange', listener, false)\n        window.addEventListener('focus', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n          window.removeEventListener('focus', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.focused !== focused\n    if (changed) {\n      this.focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.focused === 'boolean') {\n      return this.focused\n    }\n\n    // document global can be unavailable in react native\n    if (typeof document === 'undefined') {\n      return true\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(\n      document.visibilityState,\n    )\n  }\n}\n\nexport const focusManager = new FocusManager()\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setOnline: (online?: boolean) => void,\n) => (() => void) | undefined\n\nconst onlineEvents = ['online', 'offline'] as const\n\nexport class OnlineManager extends Subscribable {\n  private online?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline()\n        // Listen to online\n        onlineEvents.forEach((event) => {\n          window.addEventListener(event, listener, false)\n        })\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach((event) => {\n            window.removeEventListener(event, listener)\n          })\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((online?: boolean) => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online)\n      } else {\n        this.onOnline()\n      }\n    })\n  }\n\n  setOnline(online?: boolean): void {\n    const changed = this.online !== online\n\n    if (changed) {\n      this.online = online\n      this.onOnline()\n    }\n  }\n\n  onOnline(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isOnline(): boolean {\n    if (typeof this.online === 'boolean') {\n      return this.online\n    }\n\n    if (\n      typeof navigator === 'undefined' ||\n      typeof navigator.onLine === 'undefined'\n    ) {\n      return true\n    }\n\n    return navigator.onLine\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n", "import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { sleep } from './utils'\nimport type { CancelOptions, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = unknown> {\n  fn: () => TData | Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = unknown>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => boolean) | undefined\n  let promiseResolve: (data: TData) => void\n  let promiseReject: (error: TError) => void\n\n  const promise = new Promise<TData>((outerResolve, outerReject) => {\n    promiseResolve = outerResolve\n    promiseReject = outerReject\n  })\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const shouldPause = () =>\n    !focusManager.isFocused() ||\n    (config.networkMode !== 'always' && !onlineManager.isOnline())\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      promiseResolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      promiseReject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        const canContinue = isResolved || !shouldPause()\n        if (canContinue) {\n          continueResolve(value)\n        }\n        return canContinue\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // Execute query\n    try {\n      promiseOrValue = config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? 3\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            if (shouldPause()) {\n              return pause()\n            }\n            return\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  // Start loop\n  if (canFetch(config.networkMode)) {\n    run()\n  } else {\n    pause().then(run)\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn?.()\n      return didContinue ? promise : Promise.resolve()\n    },\n    cancelRetry,\n    continueRetry,\n  }\n}\n", "export interface Logger {\n  log: LogFunction\n  warn: LogFunction\n  error: LogFunction\n}\n\ntype LogFunction = (...args: any[]) => void\n\nexport const defaultLogger: Logger = console\n", "import { scheduleMicrotask } from './utils'\n\n// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends unknown[]> = (...args: T) => void\n\nexport function createNotifyManager() {\n  let queue: NotifyCallback[] = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n\n  const batch = <T>(callback: () => T): T => {\n    let result\n    transactions++\n    try {\n      result = callback()\n    } finally {\n      transactions--\n      if (!transactions) {\n        flush()\n      }\n    }\n    return result\n  }\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  const batchCalls = <T extends unknown[]>(\n    callback: BatchCallsCallback<T>,\n  ): BatchCallsCallback<T> => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args)\n      })\n    }\n  }\n\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  const setNotifyFunction = (fn: NotifyFunction) => {\n    notifyFn = fn\n  }\n\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  const setBatchNotifyFunction = (fn: BatchNotifyFunction) => {\n    batchNotifyFn = fn\n  }\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction,\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n", "import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  cacheTime!: number\n  private gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  protected updateCacheTime(newCacheTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      newCacheTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout)\n      this.gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n", "import { getAbortController, noop, replaceData, timeUntilStale } from './utils'\nimport { defaultLogger } from './logger'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type {\n  CancelOptions,\n  FetchStatus,\n  InitialDataFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport type { Logger } from './logger'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  logger?: Logger\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: any\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal?: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  isFetchingOptimistic?: boolean\n\n  private cache: QueryCache\n  private logger: Logger\n  private promise?: Promise<TData>\n  private retryer?: Retryer<TData>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.abortSignalConsumed = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.logger = config.logger || defaultLogger\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || getDefaultState(this.options)\n    this.state = this.initialState\n    this.scheduleGc()\n  }\n\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some((observer) => observer.options.enabled !== false)\n  }\n\n  isDisabled(): boolean {\n    return this.getObserversCount() > 0 && !this.isActive()\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some((observer) => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true\n            return abortController.signal\n          }\n          return undefined\n        },\n      })\n    }\n\n    addSignalProperty(queryFnContext)\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\n          `Missing queryFn for queryKey '${this.options.queryHash}'`,\n        )\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(context)\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      }\n      this.isFetchingOptimistic = false\n    }\n\n    // Try to fetch the data\n    this.retryer = createRetryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc()\n        }\n        this.isFetchingOptimistic = false\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: action.meta ?? null,\n            fetchStatus: canFetch(this.options.networkMode)\n              ? 'fetching'\n              : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading',\n            }),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error as unknown\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error: error as TError,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error as TError,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = typeof data !== 'undefined'\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle',\n  }\n}\n", "import { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type { NotifyEvent, OmitKeyof, QueryKey, QueryOptions } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (error: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface QueryHashMap {\n  [hash: string]: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  config: QueryCacheConfig\n\n  private queries: Query<any, any, any, any>[]\n  private queriesMap: QueryHashMap\n\n  constructor(config?: QueryCacheConfig) {\n    super()\n    this.config = config || {}\n    this.queries = []\n    this.queriesMap = {}\n  }\n\n  build<TQueryFnData, TError, TData, TQueryKey extends QueryKey>(\n    client: QueryClient,\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey!\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query\n      this.queries.push(query)\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.queriesMap[query.queryHash]\n\n    if (queryInMap) {\n      query.destroy()\n\n      this.queries = this.queries.filter((x) => x !== query)\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash]\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.queriesMap[queryHash]\n  }\n\n  getAll(): Query[] {\n    return this.queries\n  }\n\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    filters: QueryFilters,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const [filters] = parseFilterArgs(arg1, arg2)\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.queries.find((query) => matchQuery(filters, query))\n  }\n\n  findAll(filters?: QueryFilters): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[] {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    return Object.keys(filters).length > 0\n      ? this.queries.filter((query) => matchQuery(filters, query))\n      : this.queries\n  }\n\n  notify(event: QueryCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n", "import { defaultLogger } from './logger'\nimport { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { canFetch, createRetry<PERSON> } from './retryer'\nimport type { MutationMeta, MutationOptions, MutationStatus } from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { Logger } from './logger'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  logger?: Logger\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n  meta?: MutationMeta\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface LoadingAction<TVariables, TContext> {\n  type: 'loading'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError, TVariables, TContext> {\n  type: 'setState'\n  state: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | LoadingAction<TVariables, TContext>\n  | PauseAction\n  | SetStateAction<TData, TError, TVariables, TContext>\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  mutationId: number\n\n  private observers: MutationObserver<TData, TError, TVariables, TContext>[]\n  private defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  private mutationCache: MutationCache\n  private logger: Logger\n  private retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.defaultOptions = config.defaultOptions\n    this.mutationId = config.mutationId\n    this.mutationCache = config.mutationCache\n    this.logger = config.logger || defaultLogger\n    this.observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options?: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  setState(state: MutationState<TData, TError, TVariables, TContext>): void {\n    this.dispatch({ type: 'setState', state })\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.observers = this.observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc()\n      } else {\n        this.mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return this.retryer?.continue() ?? this.execute()\n  }\n\n  async execute(): Promise<TData> {\n    const executeMutation = () => {\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found')\n          }\n          return this.options.mutationFn(this.state.variables!)\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({ type: 'failed', failureCount, error })\n        },\n        onPause: () => {\n          this.dispatch({ type: 'pause' })\n        },\n        onContinue: () => {\n          this.dispatch({ type: 'continue' })\n        },\n        retry: this.options.retry ?? 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode,\n      })\n\n      return this.retryer.promise\n    }\n\n    const restored = this.state.status === 'loading'\n    try {\n      if (!restored) {\n        this.dispatch({ type: 'loading', variables: this.options.variables! })\n        // Notify cache callback\n        await this.mutationCache.config.onMutate?.(\n          this.state.variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(this.state.variables!)\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables,\n          })\n        }\n      }\n      const data = await executeMutation()\n\n      // Notify cache callback\n      await this.mutationCache.config.onSuccess?.(\n        data,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(\n        data,\n        this.state.variables!,\n        this.state.context!,\n      )\n\n      // Notify cache callback\n      await this.mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(\n        data,\n        null,\n        this.state.variables!,\n        this.state.context,\n      )\n\n      this.dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.mutationCache.config.onError?.(\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n\n        await this.options.onError?.(\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.mutationCache.config.onSettled?.(\n          undefined,\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.dispatch({ type: 'error', error: error as TError })\n      }\n    }\n  }\n\n  private dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'loading':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n  }\n}\n", "import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\ntype MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  config: MutationCacheConfig\n\n  private mutations: Mutation<any, any, any, any>[]\n  private mutationId: number\n  private resuming: Promise<unknown> | undefined\n\n  constructor(config?: MutationCacheConfig) {\n    super()\n    this.config = config || {}\n    this.mutations = []\n    this.mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey\n        ? client.getMutationDefaults(options.mutationKey)\n        : undefined,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.mutations.push(mutation)\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    this.mutations = this.mutations.filter((x) => x !== mutation)\n    this.notify({ type: 'removed', mutation })\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.mutations.forEach((mutation) => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Mutation[] {\n    return this.mutations\n  }\n\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.mutations.find((mutation) => matchMutation(filters, mutation))\n  }\n\n  findAll(filters: MutationFilters): Mutation[] {\n    return this.mutations.filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    this.resuming = (this.resuming ?? Promise.resolve())\n      .then(() => {\n        const pausedMutations = this.mutations.filter((x) => x.state.isPaused)\n        return notifyManager.batch(() =>\n          pausedMutations.reduce(\n            (promise, mutation) =>\n              promise.then(() => mutation.continue().catch(noop)),\n            Promise.resolve() as Promise<unknown>,\n          ),\n        )\n      })\n      .then(() => {\n        this.resuming = undefined\n      })\n\n    return this.resuming\n  }\n}\n", "import type { QueryBehavior } from './query'\n\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData,\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: (context) => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal?.aborted) {\n                cancelled = true\n              } else {\n                context.signal?.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn ||\n          (() =>\n            Promise.reject(\n              `Missing queryFn for queryKey '${context.options.queryHash}'`,\n            ))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean,\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean,\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then((page) =>\n            buildNewPages(pages, param, page, previous),\n          )\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then((pages) => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i]),\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then((pages) => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n  return\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n  return\n}\n", "import {\n  functionalUpdate,\n  hashQueryKey,\n  hashQueryKeyByOptions,\n  noop,\n  parseFilterArgs,\n  parseQueryArgs,\n  partialMatchKey,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport { defaultLogger } from './logger'\nimport type { OmitKeyof } from '@tanstack/query-core'\nimport type { CancelOptions, DefaultedQueryObserverOptions } from './types'\nimport type { Logger } from './logger'\nimport type { QueryState } from './query'\nimport type {\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  QueryClientConfig,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n  WithRequired,\n} from './types'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: QueryOptions<any, any, any>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  private queryCache: QueryCache\n  private mutationCache: MutationCache\n  private logger: Logger\n  private defaultOptions: DefaultOptions\n  private queryDefaults: QueryDefaults[]\n  private mutationDefaults: MutationDefaults[]\n  private mountCount: number\n  private unsubscribeFocus?: () => void\n  private unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.queryCache = config.queryCache || new QueryCache()\n    this.mutationCache = config.mutationCache || new MutationCache()\n    this.logger = config.logger || defaultLogger\n    this.defaultOptions = config.defaultOptions || {}\n    this.queryDefaults = []\n    this.mutationDefaults = []\n    this.mountCount = 0\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\n        `Passing a custom logger has been deprecated and will be removed in the next major version.`,\n      )\n    }\n  }\n\n  mount(): void {\n    this.mountCount++\n    if (this.mountCount !== 1) return\n\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations()\n        this.queryCache.onFocus()\n      }\n    })\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations()\n        this.queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.mountCount--\n    if (this.mountCount !== 0) return\n\n    this.unsubscribeFocus?.()\n    this.unsubscribeFocus = undefined\n\n    this.unsubscribeOnline?.()\n    this.unsubscribeOnline = undefined\n  }\n\n  isFetching(filters?: QueryFilters): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): number {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    filters.fetchStatus = 'fetching'\n    return this.queryCache.findAll(filters).length\n  }\n\n  isMutating(filters?: MutationFilters): number {\n    return this.mutationCache.findAll({ ...filters, fetching: true }).length\n  }\n\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined {\n    return this.queryCache.find<TQueryFnData>(queryKey, filters)?.state.data\n  }\n\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: WithRequired<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | WithRequired<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const cachedData = this.getQueryData<TData>(parsedOptions.queryKey!)\n\n    return cachedData\n      ? Promise.resolve(cachedData)\n      : this.fetchQuery(parsedOptions)\n  }\n\n  getQueriesData<TQueryFnData = unknown>(\n    filters: QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return this.getQueryCache()\n      .findAll(queryKeyOrFilters)\n      .map(({ queryKey, state }) => {\n        const data = state.data as TQueryFnData | undefined\n        return [queryKey, data]\n      })\n  }\n\n  setQueryData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): TQueryFnData | undefined {\n    const query = this.queryCache.find<TQueryFnData>(queryKey)\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (typeof data === 'undefined') {\n      return undefined\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n    return this.queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<TQueryFnData>(\n    filters: QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return notifyManager.batch(() =>\n      this.getQueryCache()\n        .findAll(queryKeyOrFilters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<TQueryFnData = unknown, TError = undefined>(\n    queryKey: QueryKey,\n    /**\n     * @deprecated This filters will be removed in the next major version.\n     */\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): QueryState<TQueryFnData, TError> | undefined {\n    return this.queryCache.find<TQueryFnData, TError>(queryKey, filters)?.state\n  }\n\n  removeQueries(filters?: QueryFilters): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    const queryCache = this.queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<ResetQueryFilters<TPageData>, 'queryKey'>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(\n    arg1?: QueryKey | ResetQueryFilters,\n    arg2?: OmitKeyof<ResetQueryFilters, 'queryKey'> | ResetOptions,\n    arg3?: ResetOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n    const queryCache = this.queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      type: 'active',\n      ...filters,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries(filters?: QueryFilters, options?: CancelOptions): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n    options?: CancelOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'> | CancelOptions,\n    arg3?: CancelOptions,\n  ): Promise<void> {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3)\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true\n    }\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(cancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<InvalidateQueryFilters<TPageData>, 'queryKey'>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(\n    arg1?: QueryKey | InvalidateQueryFilters,\n    arg2?: OmitKeyof<InvalidateQueryFilters, 'queryKey'> | InvalidateOptions,\n    arg3?: InvalidateOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    return notifyManager.batch(() => {\n      this.queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      const refetchFilters: RefetchQueryFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? 'active',\n      }\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<RefetchQueryFilters<TPageData>, 'queryKey'>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(\n    arg1?: QueryKey | RefetchQueryFilters,\n    arg2?: OmitKeyof<RefetchQueryFilters, 'queryKey'> | RefetchOptions,\n    arg3?: RefetchOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled())\n        .map((query) =>\n          query.fetch(undefined, {\n            ...options,\n            cancelRefetch: options?.cancelRefetch ?? true,\n            meta: { refetchPage: filters.refetchPage },\n          }),\n        ),\n    )\n\n    let promise = Promise.all(promises).then(noop)\n\n    if (!options?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(defaultedOptions.staleTime)\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    parsedOptions.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData\n    >()\n    return this.fetchQuery(parsedOptions)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    return this.mutationCache.resumePausedMutations()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.mutationCache\n  }\n\n  getLogger(): Logger {\n    return this.logger\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.defaultOptions = options\n  }\n\n  setQueryDefaults(\n    queryKey: QueryKey,\n    options: QueryObserverOptions<unknown, any, any, any>,\n  ): void {\n    const result = this.queryDefaults.find(\n      (x) => hashQueryKey(queryKey) === hashQueryKey(x.queryKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.queryDefaults.push({ queryKey, defaultOptions: options })\n    }\n  }\n\n  getQueryDefaults(\n    queryKey?: QueryKey,\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    if (!queryKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.queryDefaults.find((x) =>\n      partialMatchKey(queryKey, x.queryKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter((x) =>\n        partialMatchKey(queryKey, x.queryKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several query defaults match with key '${JSON.stringify(\n            queryKey,\n          )}'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  setMutationDefaults(\n    mutationKey: MutationKey,\n    options: MutationObserverOptions<any, any, any, any>,\n  ): void {\n    const result = this.mutationDefaults.find(\n      (x) => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.mutationDefaults.push({ mutationKey, defaultOptions: options })\n    }\n  }\n\n  getMutationDefaults(\n    mutationKey?: MutationKey,\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    if (!mutationKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.mutationDefaults.find((x) =>\n      partialMatchKey(mutationKey, x.mutationKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter((x) =>\n        partialMatchKey(mutationKey, x.mutationKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several mutation defaults match with key '${JSON.stringify(\n            mutationKey,\n          )}'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  defaultQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey,\n  >(\n    options?:\n      | QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options?._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options?.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options?.mutationKey),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.queryCache.clear()\n    this.mutationCache.clear()\n  }\n}\n", "import {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { canFetch, isCancelledError } from './retryer'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\nimport type { Action, FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { DefaultedQueryObserverOptions, RefetchPageFilters } from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: TError | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: ReturnType<typeof setTimeout>\n  private refetchIntervalId?: ReturnType<typeof setInterval>\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Set<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = new Set()\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryOptions(options)\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof options?.isDataEqual !== 'undefined'\n    ) {\n      this.client\n        .getLogger()\n        .error(\n          `The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option`,\n        )\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this,\n      })\n    }\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.client.getQueryCache().build(this.client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result\n      this.currentResultOptions = this.options\n      this.currentResultState = this.currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    return trackedResult\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>({\n    refetchPage,\n    ...options\n  }: RefetchOptions & RefetchPageFilters<TPageData> = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(this.client, defaultedOptions)\n    query.isFetchingOptimistic = true\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime,\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearStaleTimeout(): void {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId)\n      this.staleTimeoutId = undefined\n    }\n  }\n\n  private clearRefetchInterval(): void {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId)\n      this.refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, fetchStatus, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode)\n          ? 'fetching'\n          : 'paused'\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle'\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdatedAt &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          data = replaceData(prevResult?.data, data, options)\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError)\n          }\n          this.selectError = selectError as TError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = state.data as unknown as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      status === 'loading'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            this.selectError = null\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError)\n            }\n            this.selectError = selectError as TError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = replaceData(prevResult?.data, placeholderData, options) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = fetchStatus === 'fetching'\n    const isLoading = status === 'loading'\n    const isError = status === 'error'\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.currentResult = nextResult\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.trackedProps,\n      )\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey]\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client.getQueryCache().build(this.client, this.options)\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated',\n        })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect'],\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false\n  }\n\n  // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData\n  }\n\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n", "import { difference, replaceAt } from './utils'\nimport { notifyManager } from './notifyManager'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\nimport type {\n  DefaultedQueryObserverOptions,\n  QueryObserverOptions,\n  QueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\n\ntype QueriesObserverListener = (result: QueryObserverResult[]) => void\n\nexport class QueriesObserver extends Subscribable<QueriesObserverListener> {\n  private client: QueryClient\n  private result: QueryObserverResult[]\n  private queries: QueryObserverOptions[]\n  private observers: QueryObserver[]\n  private observersMap: Record<string, QueryObserver>\n\n  constructor(client: QueryClient, queries?: QueryObserverOptions[]) {\n    super()\n\n    this.client = client\n    this.queries = []\n    this.result = []\n    this.observers = []\n    this.observersMap = {}\n\n    if (queries) {\n      this.setQueries(queries)\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: QueryObserverOptions[],\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.queries = queries\n\n    notifyManager.batch(() => {\n      const prevObservers = this.observers\n\n      const newObserverMatches = this.findMatchingObservers(this.queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newObserversMap = Object.fromEntries(\n        newObservers.map((observer) => [observer.options.queryHash, observer]),\n      )\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.observers = newObservers\n      this.observersMap = newObserversMap\n      this.result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n\n      this.notify()\n    })\n  }\n\n  getCurrentResult(): QueryObserverResult[] {\n    return this.result\n  }\n\n  getQueries() {\n    return this.observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.observers\n  }\n\n  getOptimisticResult(queries: QueryObserverOptions[]): QueryObserverResult[] {\n    return this.findMatchingObservers(queries).map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n  }\n\n  private findMatchingObservers(\n    queries: QueryObserverOptions[],\n  ): QueryObserverMatch[] {\n    const prevObservers = this.observers\n    const prevObserversMap = new Map(\n      prevObservers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const defaultedQueryOptions = queries.map((options) =>\n      this.client.defaultQueryOptions(options),\n    )\n\n    const matchingObservers: QueryObserverMatch[] =\n      defaultedQueryOptions.flatMap((defaultedOptions) => {\n        const match = prevObserversMap.get(defaultedOptions.queryHash)\n        if (match != null) {\n          return [{ defaultedQueryOptions: defaultedOptions, observer: match }]\n        }\n        return []\n      })\n\n    const matchedQueryHashes = new Set(\n      matchingObservers.map((match) => match.defaultedQueryOptions.queryHash),\n    )\n    const unmatchedQueries = defaultedQueryOptions.filter(\n      (defaultedOptions) => !matchedQueryHashes.has(defaultedOptions.queryHash),\n    )\n\n    const matchingObserversSet = new Set(\n      matchingObservers.map((match) => match.observer),\n    )\n    const unmatchedObservers = prevObservers.filter(\n      (prevObserver) => !matchingObserversSet.has(prevObserver),\n    )\n\n    const getObserver = (options: QueryObserverOptions): QueryObserver => {\n      const defaultedOptions = this.client.defaultQueryOptions(options)\n      const currentObserver = this.observersMap[defaultedOptions.queryHash!]\n      return currentObserver ?? new QueryObserver(this.client, defaultedOptions)\n    }\n\n    const newOrReusedObservers: QueryObserverMatch[] = unmatchedQueries.map(\n      (options, index) => {\n        if (options.keepPreviousData) {\n          // return previous data from one of the observers that no longer match\n          const previouslyUsedObserver = unmatchedObservers[index]\n          if (previouslyUsedObserver !== undefined) {\n            return {\n              defaultedQueryOptions: options,\n              observer: previouslyUsedObserver,\n            }\n          }\n        }\n        return {\n          defaultedQueryOptions: options,\n          observer: getObserver(options),\n        }\n      },\n    )\n\n    const sortMatchesByOrderOfQueries = (\n      a: QueryObserverMatch,\n      b: QueryObserverMatch,\n    ): number =>\n      defaultedQueryOptions.indexOf(a.defaultedQueryOptions) -\n      defaultedQueryOptions.indexOf(b.defaultedQueryOptions)\n\n    return matchingObservers\n      .concat(newOrReusedObservers)\n      .sort(sortMatchesByOrderOfQueries)\n  }\n\n  private onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result)\n      this.notify()\n    }\n  }\n\n  private notify(): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(this.result)\n      })\n    })\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n", "import { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type {\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  QueryKey,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions, ObserverFetchOptions } from './queryObserver'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryK<PERSON>,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  InfiniteData<TData>,\n  InfiniteData<TQueryData>,\n  TQueryK<PERSON>\n> {\n  // Type override\n  subscribe!: (\n    listener?: InfiniteQueryObserverListener<TData, TError>,\n  ) => () => void\n\n  // Type override\n  getCurrentResult!: () => InfiniteQueryObserverResult<TData, TError>\n\n  // Type override\n  protected fetch!: (\n    fetchOptions: ObserverFetchOptions,\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options?: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions,\n    )\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage({ pageParam, ...options }: FetchNextPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward', pageParam },\n      },\n    })\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  }: FetchPreviousPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward', pageParam },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, InfiniteData<TQueryData>, TQueryKey>,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const result = super.createResult(query, options)\n\n    const { isFetching, isRefetching } = result\n\n    const isFetchingNextPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'forward'\n\n    const isFetchingPreviousPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'backward'\n\n    return {\n      ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data?.pages),\n      hasPreviousPage: hasPreviousPage(options, state.data?.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n  }\n}\n", "import { getDefaultState } from './mutation'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options\n    this.options = this.client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this,\n      })\n    }\n    this.currentMutation?.setOptions(this.options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const isLoading = state.status === 'loading'\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading,\n      isPending: isLoading,\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!,\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n", "import type { QueryClient } from './queryClient'\nimport type { Query, QueryState } from './query'\nimport type {\n  MutationKey,\n  MutationOptions,\n  QueryKey,\n  QueryOptions,\n} from './types'\nimport type { Mutation, MutationState } from './mutation'\n\n// TYPES\n\nexport interface DehydrateOptions {\n  dehydrateMutations?: boolean\n  dehydrateQueries?: boolean\n  shouldDehydrateMutation?: ShouldDehydrateMutationFunction\n  shouldDehydrateQuery?: ShouldDehydrateQueryFunction\n}\n\nexport interface HydrateOptions {\n  defaultOptions?: {\n    queries?: QueryOptions\n    mutations?: MutationOptions\n  }\n}\n\ninterface DehydratedMutation {\n  mutationKey?: MutationKey\n  state: MutationState\n}\n\ninterface DehydratedQuery {\n  queryHash: string\n  queryKey: QueryKey\n  state: QueryState\n}\n\nexport interface DehydratedState {\n  mutations: DehydratedMutation[]\n  queries: DehydratedQuery[]\n}\n\nexport type ShouldDehydrateQueryFunction = (query: Query) => boolean\n\nexport type ShouldDehydrateMutationFunction = (mutation: Mutation) => boolean\n\n// FUNCTIONS\n\nfunction dehydrateMutation(mutation: Mutation): DehydratedMutation {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n  }\n}\n\n// Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\nfunction dehydrateQuery(query: Query): DehydratedQuery {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n  }\n}\n\nexport function defaultShouldDehydrateMutation(mutation: Mutation) {\n  return mutation.state.isPaused\n}\n\nexport function defaultShouldDehydrateQuery(query: Query) {\n  return query.state.status === 'success'\n}\n\nexport function dehydrate(\n  client: QueryClient,\n  options: DehydrateOptions = {},\n): DehydratedState {\n  const mutations: DehydratedMutation[] = []\n  const queries: DehydratedQuery[] = []\n\n  if (options.dehydrateMutations !== false) {\n    const shouldDehydrateMutation =\n      options.shouldDehydrateMutation || defaultShouldDehydrateMutation\n\n    client\n      .getMutationCache()\n      .getAll()\n      .forEach((mutation) => {\n        if (shouldDehydrateMutation(mutation)) {\n          mutations.push(dehydrateMutation(mutation))\n        }\n      })\n  }\n\n  if (options.dehydrateQueries !== false) {\n    const shouldDehydrateQuery =\n      options.shouldDehydrateQuery || defaultShouldDehydrateQuery\n\n    client\n      .getQueryCache()\n      .getAll()\n      .forEach((query) => {\n        if (shouldDehydrateQuery(query)) {\n          queries.push(dehydrateQuery(query))\n        }\n      })\n  }\n\n  return { mutations, queries }\n}\n\nexport function hydrate(\n  client: QueryClient,\n  dehydratedState: unknown,\n  options?: HydrateOptions,\n): void {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return\n  }\n\n  const mutationCache = client.getMutationCache()\n  const queryCache = client.getQueryCache()\n\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const mutations = (dehydratedState as DehydratedState).mutations || []\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const queries = (dehydratedState as DehydratedState).queries || []\n\n  mutations.forEach((dehydratedMutation) => {\n    mutationCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.mutations,\n        mutationKey: dehydratedMutation.mutationKey,\n      },\n      dehydratedMutation.state,\n    )\n  })\n\n  queries.forEach(({ queryKey, state, queryHash }) => {\n    const query = queryCache.get(queryHash)\n\n    // Do not hydrate if an existing query exists with newer data\n    if (query) {\n      if (query.state.dataUpdatedAt < state.dataUpdatedAt) {\n        // omit fetchStatus from dehydrated state\n        // so that query stays in its current fetchStatus\n        const { fetchStatus: _ignored, ...dehydratedQueryState } = state\n        query.setState(dehydratedQueryState)\n      }\n      return\n    }\n\n    // Restore query\n    queryCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.queries,\n        queryKey,\n        queryHash,\n      },\n      // Reset fetch status to idle to avoid\n      // query being stuck in fetching state upon hydration\n      {\n        ...state,\n        fetchStatus: 'idle',\n      },\n    )\n  })\n}\n", "import * as Vue from 'vue'\n\nvar isVue2 = false\nvar isVue3 = true\nvar Vue2 = undefined\n\nfunction install() {}\n\nexport function set(target, key, val) {\n  if (Array.isArray(target)) {\n    target.length = Math.max(target.length, key)\n    target.splice(key, 1, val)\n    return val\n  }\n  target[key] = val\n  return val\n}\n\nexport function del(target, key) {\n  if (Array.isArray(target)) {\n    target.splice(key, 1)\n    return\n  }\n  delete target[key]\n}\n\nexport * from 'vue'\nexport {\n  Vue,\n  Vue2,\n  isVue2,\n  isVue3,\n  install,\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { isRef, unref } from 'vue-demi'\nimport type { Mutation<PERSON>ey, QueryKey } from '@tanstack/query-core'\nimport type { UnwrapRef } from 'vue-demi'\n\nexport const VUE_QUERY_CLIENT = 'VUE_QUERY_CLIENT'\n\nexport function getClientKey(key?: string) {\n  const suffix = key ? `:${key}` : ''\n  return `${VUE_QUERY_CLIENT}${suffix}`\n}\n\nexport function isQueryKey(value: unknown): value is QueryKey {\n  return Array.isArray(value)\n}\n\nexport function isMutationKey(value: unknown): value is MutationKey {\n  return Array.isArray(value)\n}\n\nexport function updateState(\n  state: Record<string, unknown>,\n  update: Record<string, any>,\n): void {\n  Object.keys(state).forEach((key) => {\n    state[key] = update[key]\n  })\n}\n\nexport function cloneDeep<T>(\n  value: T,\n  customizer?: (val: unknown) => unknown | void,\n): T {\n  if (customizer) {\n    const result = customizer(value)\n    if (result !== undefined || isRef(value)) {\n      return result as typeof value\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return value.map((val) => cloneDeep(val, customizer)) as typeof value\n  }\n\n  if (typeof value === 'object' && isPlainObject(value)) {\n    const entries = Object.entries(value).map(([key, val]) => [\n      key,\n      cloneDeep(val, customizer),\n    ])\n    return Object.fromEntries(entries)\n  }\n\n  return value\n}\n\nexport function cloneDeepUnref<T>(obj: T): UnwrapRef<T> {\n  return cloneDeep(obj, (val) => {\n    if (isRef(val)) {\n      return cloneDeepUnref(unref(val))\n    }\n  }) as UnwrapRef<typeof obj>\n}\n\nfunction isPlainObject(value: unknown): value is Object {\n  if (Object.prototype.toString.call(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n  return prototype === null || prototype === Object.prototype\n}\n\nexport function shouldThrowError<T extends (...args: any[]) => boolean>(\n  _useErrorBoundary: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params)\n  }\n\n  return !!_useErrorBoundary\n}\n", "import { getCurrentInstance, inject } from 'vue-demi'\n\nimport { getClientKey } from './utils'\nimport type { QueryClient } from './queryClient'\n\nexport function useQueryClient(id = ''): QueryClient {\n  const key = getClientKey(id)\n  const queryClient = inject<QueryClient | null>(key, null)\n\n  if (!queryClient) {\n    const vm = getCurrentInstance()?.proxy\n\n    if (!vm) {\n      throw new Error(\n        'vue-query hooks can only be used inside setup() function.',\n      )\n    }\n\n    throw new Error(\n      \"No 'queryClient' found in Vue context, use 'VueQueryPlugin' to properly initialize the library.\",\n    )\n  }\n\n  return queryClient\n}\n", "import { QueryCache as QC } from '@tanstack/query-core'\nimport { cloneDeepUnref, isQueryKey } from './utils'\nimport type { Query, QueryFilters, QueryKey } from '@tanstack/query-core'\nimport type { MaybeRefDeep } from './types'\n\nexport class QueryCache extends QC {\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    filters: MaybeRefDeep<QueryFilters>,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    queryKey: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<QueryFilters>,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: MaybeRefDeep<QueryKey> | MaybeRefDeep<QueryFilters>,\n    arg2?: MaybeRefDeep<QueryFilters>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const arg1Unreffed = cloneDeepUnref(arg1) as QueryKey | QueryFilters\n    const arg2Unreffed = cloneDeepUnref(arg2) as QueryFilters\n    if (isQueryKey(arg1Unreffed)) {\n      return super.find(arg1Unreffed, arg2Unreffed)\n    }\n    return super.find(arg1Unreffed)\n  }\n\n  findAll(\n    queryKey?: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<QueryFilters>,\n  ): Query[]\n  findAll(filters?: MaybeRefDeep<QueryFilters>): Query[]\n  findAll(\n    arg1?: MaybeRefDeep<QueryKey | QueryFilters>,\n    arg2?: MaybeRefDeep<QueryFilters>,\n  ): Query[]\n  findAll(\n    arg1?: MaybeRefDeep<QueryKey> | MaybeRefDeep<QueryFilters>,\n    arg2?: MaybeRefDeep<QueryFilters>,\n  ): Query[] {\n    const arg1Unreffed = cloneDeepUnref(arg1) as QueryKey | QueryFilters\n    const arg2Unreffed = cloneDeepUnref(arg2) as QueryFilters\n    if (isQueryKey(arg1Unreffed)) {\n      return super.findAll(arg1Unreffed, arg2Unreffed)\n    }\n    return super.findAll(arg1Unreffed)\n  }\n}\n", "import { MutationCache as MC } from '@tanstack/query-core'\nimport { cloneDeepUnref } from './utils'\nimport type { Mutation, MutationFilters } from '@tanstack/query-core'\nimport type { MaybeRefDeep } from './types'\n\nexport class MutationCache extends MC {\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MaybeRefDeep<MutationFilters>,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    return super.find(cloneDeepUnref(filters) as MutationFilters)\n  }\n\n  findAll(filters: MaybeRefDeep<MutationFilters>): Mutation[] {\n    return super.findAll(cloneDeepUnref(filters) as MutationFilters)\n  }\n}\n", "import { ref } from 'vue-demi'\nimport { QueryClient as QC } from '@tanstack/query-core'\nimport { cloneDeepUnref, isQuery<PERSON>ey } from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport type { MaybeRefDeep } from './types'\nimport type {\n  CancelOptions,\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationFilters,\n  MutationKey,\n  MutationObserverOptions,\n  QueryClientConfig,\n  QueryFilters,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryState,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n  Updater,\n} from '@tanstack/query-core'\n\nexport class QueryClient extends QC {\n  constructor(config: MaybeRefDeep<QueryClientConfig> = {}) {\n    const unreffedConfig = cloneDeepUnref(config) as QueryClientConfig\n    const vueQueryConfig: QueryClientConfig = {\n      logger: cloneDeepUnref(unreffedConfig.logger),\n      defaultOptions: cloneDeepUnref(unreffedConfig.defaultOptions),\n      queryCache: unreffedConfig.queryCache || new QueryCache(),\n      mutationCache: unreffedConfig.mutationCache || new MutationCache(),\n    }\n    super(vueQueryConfig)\n  }\n\n  isRestoring = ref(false)\n\n  isFetching(filters?: MaybeRefDeep<QueryFilters>): number\n  isFetching(\n    queryKey?: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<QueryFilters>,\n  ): number\n  isFetching(\n    arg1?: MaybeRefDeep<QueryFilters | QueryKey>,\n    arg2?: MaybeRefDeep<QueryFilters>,\n  ): number {\n    const arg1Unreffed = cloneDeepUnref(arg1)\n    const arg2Unreffed = cloneDeepUnref(arg2) as QueryFilters\n    if (isQueryKey(arg1Unreffed)) {\n      return super.isFetching(arg1Unreffed, arg2Unreffed)\n    }\n    return super.isFetching(arg1Unreffed as QueryFilters)\n  }\n\n  isMutating(filters?: MaybeRefDeep<MutationFilters>): number {\n    return super.isMutating(cloneDeepUnref(filters) as MutationFilters)\n  }\n\n  getQueryData<TData = unknown>(\n    queryKey: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<QueryFilters>,\n  ): TData | undefined {\n    return super.getQueryData(\n      cloneDeepUnref(queryKey),\n      cloneDeepUnref(filters) as QueryFilters,\n    )\n  }\n\n  getQueriesData<TData = unknown>(\n    queryKey: MaybeRefDeep<QueryKey>,\n  ): [QueryKey, TData | undefined][]\n  getQueriesData<TData = unknown>(\n    filters: MaybeRefDeep<QueryFilters>,\n  ): [QueryKey, TData | undefined][]\n  getQueriesData<TData = unknown>(\n    queryKeyOrFilters: MaybeRefDeep<QueryKey> | MaybeRefDeep<QueryFilters>,\n  ): [QueryKey, TData | undefined][] {\n    const unreffed = cloneDeepUnref(queryKeyOrFilters)\n    if (isQueryKey(unreffed)) {\n      return super.getQueriesData(unreffed)\n    }\n    return super.getQueriesData(unreffed as QueryFilters)\n  }\n\n  setQueryData<TData>(\n    queryKey: MaybeRefDeep<QueryKey>,\n    updater: Updater<TData | undefined, TData | undefined>,\n    options?: MaybeRefDeep<SetDataOptions>,\n  ): TData | undefined {\n    return super.setQueryData(\n      cloneDeepUnref(queryKey),\n      updater,\n      cloneDeepUnref(options) as SetDataOptions,\n    )\n  }\n\n  setQueriesData<TData>(\n    queryKey: MaybeRefDeep<QueryKey>,\n    updater: Updater<TData | undefined, TData | undefined>,\n    options?: MaybeRefDeep<SetDataOptions>,\n  ): [QueryKey, TData | undefined][]\n  setQueriesData<TData>(\n    filters: MaybeRefDeep<QueryFilters>,\n    updater: Updater<TData | undefined, TData | undefined>,\n    options?: MaybeRefDeep<SetDataOptions>,\n  ): [QueryKey, TData | undefined][]\n  setQueriesData<TData>(\n    queryKeyOrFilters: MaybeRefDeep<QueryKey | QueryFilters>,\n    updater: Updater<TData | undefined, TData | undefined>,\n    options?: MaybeRefDeep<SetDataOptions>,\n  ): [QueryKey, TData | undefined][] {\n    const arg1Unreffed = cloneDeepUnref(queryKeyOrFilters)\n    const arg3Unreffed = cloneDeepUnref(options) as SetDataOptions\n    if (isQueryKey(arg1Unreffed)) {\n      return super.setQueriesData(arg1Unreffed, updater, arg3Unreffed)\n    }\n    return super.setQueriesData(\n      arg1Unreffed as QueryFilters,\n      updater,\n      arg3Unreffed,\n    )\n  }\n\n  getQueryState<TData = unknown, TError = undefined>(\n    queryKey: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<QueryFilters>,\n  ): QueryState<TData, TError> | undefined {\n    return super.getQueryState(\n      cloneDeepUnref(queryKey),\n      cloneDeepUnref(filters) as QueryFilters,\n    )\n  }\n\n  removeQueries(filters?: MaybeRefDeep<QueryFilters>): void\n  removeQueries(\n    queryKey?: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<QueryFilters>,\n  ): void\n  removeQueries(\n    arg1?: MaybeRefDeep<QueryKey | QueryFilters>,\n    arg2?: MaybeRefDeep<QueryFilters>,\n  ): void {\n    const arg1Unreffed = cloneDeepUnref(arg1)\n    if (isQueryKey(arg1Unreffed)) {\n      return super.removeQueries(\n        arg1Unreffed,\n        cloneDeepUnref(arg2) as QueryFilters,\n      )\n    }\n    return super.removeQueries(arg1Unreffed as QueryFilters)\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: MaybeRefDeep<ResetQueryFilters<TPageData>>,\n    options?: MaybeRefDeep<ResetOptions>,\n  ): Promise<void>\n  resetQueries<TPageData = unknown>(\n    queryKey?: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<ResetQueryFilters<TPageData>>,\n    options?: MaybeRefDeep<ResetOptions>,\n  ): Promise<void>\n  resetQueries<TPageData = unknown>(\n    arg1?: MaybeRefDeep<QueryKey | ResetQueryFilters<TPageData>>,\n    arg2?: MaybeRefDeep<ResetQueryFilters<TPageData> | ResetOptions>,\n    arg3?: MaybeRefDeep<ResetOptions>,\n  ): Promise<void> {\n    const arg1Unreffed = cloneDeepUnref(arg1)\n    const arg2Unreffed = cloneDeepUnref(arg2)\n    if (isQueryKey(arg1Unreffed)) {\n      return super.resetQueries(\n        arg1Unreffed,\n        arg2Unreffed as ResetQueryFilters<TPageData> | undefined,\n        cloneDeepUnref(arg3) as ResetOptions,\n      )\n    }\n    return super.resetQueries(\n      arg1Unreffed as ResetQueryFilters<TPageData>,\n      arg2Unreffed as ResetOptions,\n    )\n  }\n\n  cancelQueries(\n    filters?: MaybeRefDeep<QueryFilters>,\n    options?: MaybeRefDeep<CancelOptions>,\n  ): Promise<void>\n  cancelQueries(\n    queryKey?: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<QueryFilters>,\n    options?: MaybeRefDeep<CancelOptions>,\n  ): Promise<void>\n  cancelQueries(\n    arg1?: MaybeRefDeep<QueryKey | QueryFilters>,\n    arg2?: MaybeRefDeep<QueryFilters | CancelOptions>,\n    arg3?: MaybeRefDeep<CancelOptions>,\n  ): Promise<void> {\n    const arg1Unreffed = cloneDeepUnref(arg1)\n    const arg2Unreffed = cloneDeepUnref(arg2)\n    if (isQueryKey(arg1Unreffed)) {\n      return super.cancelQueries(\n        arg1Unreffed,\n        arg2Unreffed as QueryFilters | undefined,\n        cloneDeepUnref(arg3) as CancelOptions,\n      )\n    }\n    return super.cancelQueries(\n      arg1Unreffed as QueryFilters,\n      arg2Unreffed as CancelOptions,\n    )\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: MaybeRefDeep<InvalidateQueryFilters<TPageData>>,\n    options?: MaybeRefDeep<InvalidateOptions>,\n  ): Promise<void>\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<InvalidateQueryFilters<TPageData>>,\n    options?: MaybeRefDeep<InvalidateOptions>,\n  ): Promise<void>\n  invalidateQueries<TPageData = unknown>(\n    arg1?: MaybeRefDeep<QueryKey | InvalidateQueryFilters<TPageData>>,\n    arg2?: MaybeRefDeep<InvalidateQueryFilters<TPageData> | InvalidateOptions>,\n    arg3?: MaybeRefDeep<InvalidateOptions>,\n  ): Promise<void> {\n    const arg1Unreffed = cloneDeepUnref(arg1)\n    const arg2Unreffed = cloneDeepUnref(arg2)\n    if (isQueryKey(arg1Unreffed)) {\n      return super.invalidateQueries(\n        arg1Unreffed,\n        arg2Unreffed as InvalidateQueryFilters | undefined,\n        cloneDeepUnref(arg3) as InvalidateOptions,\n      )\n    }\n    return super.invalidateQueries(\n      arg1Unreffed as InvalidateQueryFilters<TPageData>,\n      arg2Unreffed as InvalidateOptions,\n    )\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: MaybeRefDeep<RefetchQueryFilters<TPageData>>,\n    options?: MaybeRefDeep<RefetchOptions>,\n  ): Promise<void>\n  refetchQueries<TPageData = unknown>(\n    queryKey?: MaybeRefDeep<QueryKey>,\n    filters?: MaybeRefDeep<RefetchQueryFilters<TPageData>>,\n    options?: MaybeRefDeep<RefetchOptions>,\n  ): Promise<void>\n  refetchQueries<TPageData = unknown>(\n    arg1?: MaybeRefDeep<QueryKey | RefetchQueryFilters<TPageData>>,\n    arg2?: MaybeRefDeep<RefetchQueryFilters<TPageData> | RefetchOptions>,\n    arg3?: MaybeRefDeep<RefetchOptions>,\n  ): Promise<void> {\n    const arg1Unreffed = cloneDeepUnref(arg1)\n    const arg2Unreffed = cloneDeepUnref(arg2)\n    if (isQueryKey(arg1Unreffed)) {\n      return super.refetchQueries(\n        arg1Unreffed,\n        arg2Unreffed as RefetchQueryFilters | undefined,\n        cloneDeepUnref(arg3) as RefetchOptions,\n      )\n    }\n    return super.refetchQueries(\n      arg1Unreffed as RefetchQueryFilters<TPageData>,\n      arg2Unreffed as RefetchOptions,\n    )\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: MaybeRefDeep<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: MaybeRefDeep<TQueryKey>,\n    options?: MaybeRefDeep<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: MaybeRefDeep<TQueryKey>,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: MaybeRefDeep<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | MaybeRefDeep<TQueryKey>\n      | MaybeRefDeep<FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | MaybeRefDeep<FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>>,\n    arg3?: MaybeRefDeep<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<TData> {\n    const arg1Unreffed = cloneDeepUnref(arg1)\n    const arg2Unreffed = cloneDeepUnref(arg2)\n    if (isQueryKey(arg1Unreffed)) {\n      return super.fetchQuery(\n        arg1Unreffed as TQueryKey,\n        arg2Unreffed as QueryFunction<TQueryFnData, TQueryKey>,\n        cloneDeepUnref(arg3) as FetchQueryOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryKey\n        >,\n      )\n    }\n    return super.fetchQuery(\n      arg1Unreffed as FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    )\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: MaybeRefDeep<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: MaybeRefDeep<TQueryKey>,\n    options?: MaybeRefDeep<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: MaybeRefDeep<TQueryKey>,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: MaybeRefDeep<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: MaybeRefDeep<\n      TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | MaybeRefDeep<FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>>,\n    arg3?: MaybeRefDeep<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<void> {\n    return super.prefetchQuery(\n      cloneDeepUnref(arg1) as any,\n      cloneDeepUnref(arg2) as any,\n      cloneDeepUnref(arg3) as any,\n    )\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: MaybeRefDeep<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: MaybeRefDeep<TQueryKey>,\n    options?: MaybeRefDeep<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: MaybeRefDeep<TQueryKey>,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: MaybeRefDeep<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: MaybeRefDeep<\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | MaybeRefDeep<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n        >,\n    arg3?: MaybeRefDeep<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<InfiniteData<TData>> {\n    const arg1Unreffed = cloneDeepUnref(arg1)\n    const arg2Unreffed = cloneDeepUnref(arg2)\n    if (isQueryKey(arg1Unreffed)) {\n      return super.fetchInfiniteQuery(\n        arg1Unreffed as TQueryKey,\n        arg2Unreffed as QueryFunction<TQueryFnData, TQueryKey>,\n        cloneDeepUnref(arg3) as FetchInfiniteQueryOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryKey\n        >,\n      )\n    }\n    return super.fetchInfiniteQuery(\n      arg1Unreffed as FetchInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryKey\n      >,\n    )\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: MaybeRefDeep<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: MaybeRefDeep<TQueryKey>,\n    options?: MaybeRefDeep<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: MaybeRefDeep<TQueryKey>,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: MaybeRefDeep<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: MaybeRefDeep<\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | MaybeRefDeep<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n        >,\n    arg3?: MaybeRefDeep<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n    >,\n  ): Promise<void> {\n    return super.prefetchInfiniteQuery(\n      cloneDeepUnref(arg1) as any,\n      cloneDeepUnref(arg2) as any,\n      cloneDeepUnref(arg3) as any,\n    )\n  }\n\n  setDefaultOptions(options: MaybeRefDeep<DefaultOptions>): void {\n    super.setDefaultOptions(cloneDeepUnref(options) as DefaultOptions)\n  }\n\n  setQueryDefaults(\n    queryKey: MaybeRefDeep<QueryKey>,\n    options: MaybeRefDeep<QueryObserverOptions<any, any, any, any>>,\n  ): void {\n    super.setQueryDefaults(\n      cloneDeepUnref(queryKey),\n      cloneDeepUnref(options) as any,\n    )\n  }\n\n  getQueryDefaults(\n    queryKey?: MaybeRefDeep<QueryKey>,\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    return super.getQueryDefaults(cloneDeepUnref(queryKey))\n  }\n\n  setMutationDefaults(\n    mutationKey: MaybeRefDeep<MutationKey>,\n    options: MaybeRefDeep<MutationObserverOptions<any, any, any, any>>,\n  ): void {\n    super.setMutationDefaults(\n      cloneDeepUnref(mutationKey),\n      cloneDeepUnref(options) as any,\n    )\n  }\n\n  getMutationDefaults(\n    mutationKey?: MaybeRefDeep<MutationKey>,\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    return super.getMutationDefaults(cloneDeepUnref(mutationKey))\n  }\n}\n", "const characterMap: Record<string, string> = {\n  À: 'A',\n  Á: 'A',\n  Â: 'A',\n  Ã: 'A',\n  Ä: 'A',\n  Å: 'A',\n  Ấ: 'A',\n  Ắ: 'A',\n  Ẳ: 'A',\n  Ẵ: 'A',\n  Ặ: 'A',\n  Æ: 'AE',\n  Ầ: 'A',\n  Ằ: 'A',\n  Ȃ: 'A',\n  Ç: 'C',\n  Ḉ: 'C',\n  È: 'E',\n  É: 'E',\n  Ê: 'E',\n  Ë: 'E',\n  Ế: 'E',\n  Ḗ: 'E',\n  Ề: 'E',\n  Ḕ: 'E',\n  Ḝ: 'E',\n  Ȇ: 'E',\n  Ì: 'I',\n  Í: 'I',\n  Î: 'I',\n  Ï: 'I',\n  Ḯ: 'I',\n  Ȋ: 'I',\n  Ð: 'D',\n  Ñ: 'N',\n  Ò: 'O',\n  Ó: 'O',\n  Ô: 'O',\n  Õ: 'O',\n  Ö: 'O',\n  Ø: 'O',\n  Ố: 'O',\n  Ṍ: 'O',\n  Ṓ: 'O',\n  Ȏ: 'O',\n  Ù: 'U',\n  Ú: 'U',\n  Û: 'U',\n  Ü: 'U',\n  Ý: 'Y',\n  à: 'a',\n  á: 'a',\n  â: 'a',\n  ã: 'a',\n  ä: 'a',\n  å: 'a',\n  ấ: 'a',\n  ắ: 'a',\n  ẳ: 'a',\n  ẵ: 'a',\n  ặ: 'a',\n  æ: 'ae',\n  ầ: 'a',\n  ằ: 'a',\n  ȃ: 'a',\n  ç: 'c',\n  ḉ: 'c',\n  è: 'e',\n  é: 'e',\n  ê: 'e',\n  ë: 'e',\n  ế: 'e',\n  ḗ: 'e',\n  ề: 'e',\n  ḕ: 'e',\n  ḝ: 'e',\n  ȇ: 'e',\n  ì: 'i',\n  í: 'i',\n  î: 'i',\n  ï: 'i',\n  ḯ: 'i',\n  ȋ: 'i',\n  ð: 'd',\n  ñ: 'n',\n  ò: 'o',\n  ó: 'o',\n  ô: 'o',\n  õ: 'o',\n  ö: 'o',\n  ø: 'o',\n  ố: 'o',\n  ṍ: 'o',\n  ṓ: 'o',\n  ȏ: 'o',\n  ù: 'u',\n  ú: 'u',\n  û: 'u',\n  ü: 'u',\n  ý: 'y',\n  ÿ: 'y',\n  Ā: 'A',\n  ā: 'a',\n  Ă: 'A',\n  ă: 'a',\n  Ą: 'A',\n  ą: 'a',\n  Ć: 'C',\n  ć: 'c',\n  Ĉ: 'C',\n  ĉ: 'c',\n  Ċ: 'C',\n  ċ: 'c',\n  Č: 'C',\n  č: 'c',\n  C̆: 'C',\n  c̆: 'c',\n  Ď: 'D',\n  ď: 'd',\n  Đ: 'D',\n  đ: 'd',\n  Ē: 'E',\n  ē: 'e',\n  Ĕ: 'E',\n  ĕ: 'e',\n  Ė: 'E',\n  ė: 'e',\n  Ę: 'E',\n  ę: 'e',\n  Ě: 'E',\n  ě: 'e',\n  Ĝ: 'G',\n  Ǵ: 'G',\n  ĝ: 'g',\n  ǵ: 'g',\n  Ğ: 'G',\n  ğ: 'g',\n  Ġ: 'G',\n  ġ: 'g',\n  Ģ: 'G',\n  ģ: 'g',\n  Ĥ: 'H',\n  ĥ: 'h',\n  Ħ: 'H',\n  ħ: 'h',\n  Ḫ: 'H',\n  ḫ: 'h',\n  Ĩ: 'I',\n  ĩ: 'i',\n  Ī: 'I',\n  ī: 'i',\n  Ĭ: 'I',\n  ĭ: 'i',\n  Į: 'I',\n  į: 'i',\n  İ: 'I',\n  ı: 'i',\n  Ĳ: 'IJ',\n  ĳ: 'ij',\n  Ĵ: 'J',\n  ĵ: 'j',\n  Ķ: 'K',\n  ķ: 'k',\n  Ḱ: 'K',\n  ḱ: 'k',\n  K̆: 'K',\n  k̆: 'k',\n  Ĺ: 'L',\n  ĺ: 'l',\n  Ļ: 'L',\n  ļ: 'l',\n  Ľ: 'L',\n  ľ: 'l',\n  Ŀ: 'L',\n  ŀ: 'l',\n  Ł: 'l',\n  ł: 'l',\n  Ḿ: 'M',\n  ḿ: 'm',\n  M̆: 'M',\n  m̆: 'm',\n  Ń: 'N',\n  ń: 'n',\n  Ņ: 'N',\n  ņ: 'n',\n  Ň: 'N',\n  ň: 'n',\n  ŉ: 'n',\n  N̆: 'N',\n  n̆: 'n',\n  Ō: 'O',\n  ō: 'o',\n  Ŏ: 'O',\n  ŏ: 'o',\n  Ő: 'O',\n  ő: 'o',\n  Œ: 'OE',\n  œ: 'oe',\n  P̆: 'P',\n  p̆: 'p',\n  Ŕ: 'R',\n  ŕ: 'r',\n  Ŗ: 'R',\n  ŗ: 'r',\n  Ř: 'R',\n  ř: 'r',\n  R̆: 'R',\n  r̆: 'r',\n  Ȓ: 'R',\n  ȓ: 'r',\n  Ś: 'S',\n  ś: 's',\n  Ŝ: 'S',\n  ŝ: 's',\n  Ş: 'S',\n  Ș: 'S',\n  ș: 's',\n  ş: 's',\n  Š: 'S',\n  š: 's',\n  Ţ: 'T',\n  ţ: 't',\n  ț: 't',\n  Ț: 'T',\n  Ť: 'T',\n  ť: 't',\n  Ŧ: 'T',\n  ŧ: 't',\n  T̆: 'T',\n  t̆: 't',\n  Ũ: 'U',\n  ũ: 'u',\n  Ū: 'U',\n  ū: 'u',\n  Ŭ: 'U',\n  ŭ: 'u',\n  Ů: 'U',\n  ů: 'u',\n  Ű: 'U',\n  ű: 'u',\n  Ų: 'U',\n  ų: 'u',\n  Ȗ: 'U',\n  ȗ: 'u',\n  V̆: 'V',\n  v̆: 'v',\n  Ŵ: 'W',\n  ŵ: 'w',\n  Ẃ: 'W',\n  ẃ: 'w',\n  X̆: 'X',\n  x̆: 'x',\n  Ŷ: 'Y',\n  ŷ: 'y',\n  Ÿ: 'Y',\n  Y̆: 'Y',\n  y̆: 'y',\n  Ź: 'Z',\n  ź: 'z',\n  Ż: 'Z',\n  ż: 'z',\n  Ž: 'Z',\n  ž: 'z',\n  ſ: 's',\n  ƒ: 'f',\n  Ơ: 'O',\n  ơ: 'o',\n  Ư: 'U',\n  ư: 'u',\n  Ǎ: 'A',\n  ǎ: 'a',\n  Ǐ: 'I',\n  ǐ: 'i',\n  Ǒ: 'O',\n  ǒ: 'o',\n  Ǔ: 'U',\n  ǔ: 'u',\n  Ǖ: 'U',\n  ǖ: 'u',\n  Ǘ: 'U',\n  ǘ: 'u',\n  Ǚ: 'U',\n  ǚ: 'u',\n  Ǜ: 'U',\n  ǜ: 'u',\n  Ứ: 'U',\n  ứ: 'u',\n  Ṹ: 'U',\n  ṹ: 'u',\n  Ǻ: 'A',\n  ǻ: 'a',\n  Ǽ: 'AE',\n  ǽ: 'ae',\n  Ǿ: 'O',\n  ǿ: 'o',\n  Þ: 'TH',\n  þ: 'th',\n  Ṕ: 'P',\n  ṕ: 'p',\n  Ṥ: 'S',\n  ṥ: 's',\n  X́: 'X',\n  x́: 'x',\n  Ѓ: 'Г',\n  ѓ: 'г',\n  Ќ: 'К',\n  ќ: 'к',\n  A̋: 'A',\n  a̋: 'a',\n  E̋: 'E',\n  e̋: 'e',\n  I̋: 'I',\n  i̋: 'i',\n  Ǹ: 'N',\n  ǹ: 'n',\n  Ồ: 'O',\n  ồ: 'o',\n  Ṑ: 'O',\n  ṑ: 'o',\n  Ừ: 'U',\n  ừ: 'u',\n  Ẁ: 'W',\n  ẁ: 'w',\n  Ỳ: 'Y',\n  ỳ: 'y',\n  Ȁ: 'A',\n  ȁ: 'a',\n  Ȅ: 'E',\n  ȅ: 'e',\n  Ȉ: 'I',\n  ȉ: 'i',\n  Ȍ: 'O',\n  ȍ: 'o',\n  Ȑ: 'R',\n  ȑ: 'r',\n  Ȕ: 'U',\n  ȕ: 'u',\n  B̌: 'B',\n  b̌: 'b',\n  Č̣: 'C',\n  č̣: 'c',\n  Ê̌: 'E',\n  ê̌: 'e',\n  F̌: 'F',\n  f̌: 'f',\n  Ǧ: 'G',\n  ǧ: 'g',\n  Ȟ: 'H',\n  ȟ: 'h',\n  J̌: 'J',\n  ǰ: 'j',\n  Ǩ: 'K',\n  ǩ: 'k',\n  M̌: 'M',\n  m̌: 'm',\n  P̌: 'P',\n  p̌: 'p',\n  Q̌: 'Q',\n  q̌: 'q',\n  Ř̩: 'R',\n  ř̩: 'r',\n  Ṧ: 'S',\n  ṧ: 's',\n  V̌: 'V',\n  v̌: 'v',\n  W̌: 'W',\n  w̌: 'w',\n  X̌: 'X',\n  x̌: 'x',\n  Y̌: 'Y',\n  y̌: 'y',\n  A̧: 'A',\n  a̧: 'a',\n  B̧: 'B',\n  b̧: 'b',\n  Ḑ: 'D',\n  ḑ: 'd',\n  Ȩ: 'E',\n  ȩ: 'e',\n  Ɛ̧: 'E',\n  ɛ̧: 'e',\n  Ḩ: 'H',\n  ḩ: 'h',\n  I̧: 'I',\n  i̧: 'i',\n  Ɨ̧: 'I',\n  ɨ̧: 'i',\n  M̧: 'M',\n  m̧: 'm',\n  O̧: 'O',\n  o̧: 'o',\n  Q̧: 'Q',\n  q̧: 'q',\n  U̧: 'U',\n  u̧: 'u',\n  X̧: 'X',\n  x̧: 'x',\n  Z̧: 'Z',\n  z̧: 'z',\n}\n\nconst chars = Object.keys(characterMap).join('|')\nconst allAccents = new RegExp(chars, 'g')\n\nexport function removeAccents(str: string) {\n  return str.replace(allAccents, match => {\n    return characterMap[match]!\n  })\n}\n", "/**\n * @name match-sorter\n * @license MIT license.\n * @copyright (c) 2099 Kent <PERSON><PERSON>\n * <AUTHOR> <<EMAIL>> (https://kentcdodds.com)\n */\n\n// This is a fork of match-sorter. Instead of offering\n// a unified API for filtering and sorting in a single pass,\n// match-sorter-utils provides the lower-level utilities of\n// ranking items and comparing ranks in a way that can\n// be incrementally applied to a system rather than\n// all-at-once.\n\n// 1. Use the rankItem function to rank an item\n// 2. Use the resulting rankingInfo.passed to filter\n// 3. Use the resulting rankingInfo.rank to sort\n\n// For bundling purposes (mainly remove-accents not being esm safe/ready),\n// we've also hard-coded remove-accents into this source.\n// The remove-accents package is still included as a dependency\n// for attribution purposes, but it will not be imported and bundled.\n\nimport { removeAccents } from './remove-accents'\n\nexport type AccessorAttributes = {\n  threshold?: Ranking\n  maxRanking: Ranking\n  minRanking: Ranking\n}\n\nexport interface RankingInfo {\n  rankedValue: any\n  rank: Ranking\n  accessorIndex: number\n  accessorThreshold: Ranking | undefined\n  passed: boolean\n}\n\nexport interface AccessorOptions<TItem> {\n  accessor: AccessorFn<TItem>\n  threshold?: Ranking\n  maxRanking?: Ranking\n  minRanking?: Ranking\n}\n\nexport type AccessorFn<TItem> = (item: TItem) => string | Array<string>\n\nexport type Accessor<TItem> = AccessorFn<TItem> | AccessorOptions<TItem>\n\nexport interface RankItemOptions<TItem = unknown> {\n  accessors?: ReadonlyArray<Accessor<TItem>>\n  threshold?: Ranking\n  keepDiacritics?: boolean\n}\n\nexport const rankings = {\n  CASE_SENSITIVE_EQUAL: 7,\n  EQUAL: 6,\n  STARTS_WITH: 5,\n  WORD_STARTS_WITH: 4,\n  CONTAINS: 3,\n  ACRONYM: 2,\n  MATCHES: 1,\n  NO_MATCH: 0,\n} as const\n\nexport type Ranking = (typeof rankings)[keyof typeof rankings]\n\n/**\n * Gets the highest ranking for value for the given item based on its values for the given keys\n * @param {*} item - the item to rank\n * @param {String} value - the value to rank against\n * @param {Object} options - options to control the ranking\n * @return {{rank: Number, accessorIndex: Number, accessorThreshold: Number}} - the highest ranking\n */\nexport function rankItem<TItem>(\n  item: TItem,\n  value: string,\n  options?: RankItemOptions<TItem>\n): RankingInfo {\n  options = options || {}\n\n  options.threshold = options.threshold ?? rankings.MATCHES\n\n  if (!options.accessors) {\n    // if keys is not specified, then we assume the item given is ready to be matched\n    const rank = getMatchRanking(item as unknown as string, value, options)\n    return {\n      // ends up being duplicate of 'item' in matches but consistent\n      rankedValue: item,\n      rank,\n      accessorIndex: -1,\n      accessorThreshold: options.threshold,\n      passed: rank >= options.threshold,\n    }\n  }\n\n  const valuesToRank = getAllValuesToRank(item, options.accessors)\n\n  const rankingInfo: RankingInfo = {\n    rankedValue: item,\n    rank: rankings.NO_MATCH as Ranking,\n    accessorIndex: -1,\n    accessorThreshold: options.threshold,\n    passed: false,\n  }\n\n  for (let i = 0; i < valuesToRank.length; i++) {\n    const rankValue = valuesToRank[i]!\n\n    let newRank = getMatchRanking(rankValue.itemValue, value, options)\n\n    const {\n      minRanking,\n      maxRanking,\n      threshold = options.threshold,\n    } = rankValue.attributes\n\n    if (newRank < minRanking && newRank >= rankings.MATCHES) {\n      newRank = minRanking\n    } else if (newRank > maxRanking) {\n      newRank = maxRanking\n    }\n\n    newRank = Math.min(newRank, maxRanking) as Ranking\n\n    if (newRank >= threshold && newRank > rankingInfo.rank) {\n      rankingInfo.rank = newRank\n      rankingInfo.passed = true\n      rankingInfo.accessorIndex = i\n      rankingInfo.accessorThreshold = threshold\n      rankingInfo.rankedValue = rankValue.itemValue\n    }\n  }\n\n  return rankingInfo\n}\n\n/**\n * Gives a rankings score based on how well the two strings match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @param {Object} options - options for the match (like keepDiacritics for comparison)\n * @returns {Number} the ranking for how well stringToRank matches testString\n */\nfunction getMatchRanking<TItem>(\n  testString: string,\n  stringToRank: string,\n  options: RankItemOptions<TItem>\n): Ranking {\n  testString = prepareValueForComparison(testString, options)\n  stringToRank = prepareValueForComparison(stringToRank, options)\n\n  // too long\n  if (stringToRank.length > testString.length) {\n    return rankings.NO_MATCH\n  }\n\n  // case sensitive equals\n  if (testString === stringToRank) {\n    return rankings.CASE_SENSITIVE_EQUAL\n  }\n\n  // Lower casing before further comparison\n  testString = testString.toLowerCase()\n  stringToRank = stringToRank.toLowerCase()\n\n  // case insensitive equals\n  if (testString === stringToRank) {\n    return rankings.EQUAL\n  }\n\n  // starts with\n  if (testString.startsWith(stringToRank)) {\n    return rankings.STARTS_WITH\n  }\n\n  // word starts with\n  if (testString.includes(` ${stringToRank}`)) {\n    return rankings.WORD_STARTS_WITH\n  }\n\n  // contains\n  if (testString.includes(stringToRank)) {\n    return rankings.CONTAINS\n  } else if (stringToRank.length === 1) {\n    // If the only character in the given stringToRank\n    //   isn't even contained in the testString, then\n    //   it's definitely not a match.\n    return rankings.NO_MATCH\n  }\n\n  // acronym\n  if (getAcronym(testString).includes(stringToRank)) {\n    return rankings.ACRONYM\n  }\n\n  // will return a number between rankings.MATCHES and\n  // rankings.MATCHES + 1 depending  on how close of a match it is.\n  return getClosenessRanking(testString, stringToRank)\n}\n\n/**\n * Generates an acronym for a string.\n *\n * @param {String} string the string for which to produce the acronym\n * @returns {String} the acronym\n */\nfunction getAcronym(string: string): string {\n  let acronym = ''\n  const wordsInString = string.split(' ')\n  wordsInString.forEach(wordInString => {\n    const splitByHyphenWords = wordInString.split('-')\n    splitByHyphenWords.forEach(splitByHyphenWord => {\n      acronym += splitByHyphenWord.substr(0, 1)\n    })\n  })\n  return acronym\n}\n\n/**\n * Returns a score based on how spread apart the\n * characters from the stringToRank are within the testString.\n * A number close to rankings.MATCHES represents a loose match. A number close\n * to rankings.MATCHES + 1 represents a tighter match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @returns {Number} the number between rankings.MATCHES and\n * rankings.MATCHES + 1 for how well stringToRank matches testString\n */\nfunction getClosenessRanking(\n  testString: string,\n  stringToRank: string\n): Ranking {\n  let matchingInOrderCharCount = 0\n  let charNumber = 0\n  function findMatchingCharacter(\n    matchChar: undefined | string,\n    string: string,\n    index: number\n  ) {\n    for (let j = index, J = string.length; j < J; j++) {\n      const stringChar = string[j]\n      if (stringChar === matchChar) {\n        matchingInOrderCharCount += 1\n        return j + 1\n      }\n    }\n    return -1\n  }\n  function getRanking(spread: number) {\n    const spreadPercentage = 1 / spread\n    const inOrderPercentage = matchingInOrderCharCount / stringToRank.length\n    const ranking = rankings.MATCHES + inOrderPercentage * spreadPercentage\n    return ranking as Ranking\n  }\n  const firstIndex = findMatchingCharacter(stringToRank[0], testString, 0)\n  if (firstIndex < 0) {\n    return rankings.NO_MATCH\n  }\n  charNumber = firstIndex\n  for (let i = 1, I = stringToRank.length; i < I; i++) {\n    const matchChar = stringToRank[i]\n    charNumber = findMatchingCharacter(matchChar, testString, charNumber)\n    const found = charNumber > -1\n    if (!found) {\n      return rankings.NO_MATCH\n    }\n  }\n\n  const spread = charNumber - firstIndex\n  return getRanking(spread)\n}\n\n/**\n * Sorts items that have a rank, index, and accessorIndex\n * @param {Object} a - the first item to sort\n * @param {Object} b - the second item to sort\n * @return {Number} -1 if a should come first, 1 if b should come first, 0 if equal\n */\nexport function compareItems<TItem>(a: RankingInfo, b: RankingInfo): number {\n  return a.rank === b.rank ? 0 : a.rank > b.rank ? -1 : 1\n}\n\n/**\n * Prepares value for comparison by stringifying it, removing diacritics (if specified)\n * @param {String} value - the value to clean\n * @param {Object} options - {keepDiacritics: whether to remove diacritics}\n * @return {String} the prepared value\n */\nfunction prepareValueForComparison<TItem>(\n  value: string,\n  { keepDiacritics }: RankItemOptions<TItem>\n): string {\n  // value might not actually be a string at this point (we don't get to choose)\n  // so part of preparing the value for comparison is ensure that it is a string\n  value = `${value}` // toString\n  if (!keepDiacritics) {\n    value = removeAccents(value)\n  }\n  return value\n}\n\n/**\n * Gets value for key in item at arbitrarily nested keypath\n * @param {Object} item - the item\n * @param {Object|Function} key - the potentially nested keypath or property callback\n * @return {Array} - an array containing the value(s) at the nested keypath\n */\nfunction getItemValues<TItem>(\n  item: TItem,\n  accessor: Accessor<TItem>\n): Array<string> {\n  let accessorFn = accessor as AccessorFn<TItem>\n\n  if (typeof accessor === 'object') {\n    accessorFn = accessor.accessor\n  }\n\n  const value = accessorFn(item)\n\n  // because `value` can also be undefined\n  if (value == null) {\n    return []\n  }\n\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  return [String(value)]\n}\n\n/**\n * Gets all the values for the given keys in the given item and returns an array of those values\n * @param item - the item from which the values will be retrieved\n * @param keys - the keys to use to retrieve the values\n * @return objects with {itemValue, attributes}\n */\nfunction getAllValuesToRank<TItem>(\n  item: TItem,\n  accessors: ReadonlyArray<Accessor<TItem>>\n) {\n  const allValues: Array<{\n    itemValue: string\n    attributes: AccessorAttributes\n  }> = []\n  for (let j = 0, J = accessors.length; j < J; j++) {\n    const accessor = accessors[j]!\n    const attributes = getAccessorAttributes(accessor)\n    const itemValues = getItemValues(item, accessor)\n    for (let i = 0, I = itemValues.length; i < I; i++) {\n      allValues.push({\n        itemValue: itemValues[i]!,\n        attributes,\n      })\n    }\n  }\n  return allValues\n}\n\nconst defaultKeyAttributes = {\n  maxRanking: Infinity as Ranking,\n  minRanking: -Infinity as Ranking,\n}\n/**\n * Gets all the attributes for the given accessor\n * @param accessor - the accessor from which the attributes will be retrieved\n * @return object containing the accessor's attributes\n */\nfunction getAccessorAttributes<TItem>(\n  accessor: Accessor<TItem>\n): AccessorAttributes {\n  if (typeof accessor === 'function') {\n    return defaultKeyAttributes\n  }\n  return { ...defaultKeyAttributes, ...accessor }\n}\n", "/* istanbul ignore file */\n\nimport type { Query } from '@tanstack/query-core'\n\ntype SortFn = (a: Query, b: Query) => number\n\n// eslint-disable-next-line no-shadow\nenum QueryState {\n  Fetching = 0,\n  Fresh,\n  Stale,\n  Inactive,\n  Paused,\n}\n\nexport function getQueryState(query: Query): QueryState {\n  if (query.state.fetchStatus === 'fetching') {\n    return QueryState.Fetching\n  }\n  if (query.state.fetchStatus === 'paused') {\n    return QueryState.Paused\n  }\n  if (!query.getObserversCount()) {\n    return QueryState.Inactive\n  }\n  if (query.isStale()) {\n    return QueryState.Stale\n  }\n\n  return QueryState.Fresh\n}\n\nexport function getQueryStateLabel(query: Query): string {\n  const queryState = getQueryState(query)\n\n  if (queryState === QueryState.Fetching) {\n    return 'fetching'\n  }\n  if (queryState === QueryState.Paused) {\n    return 'paused'\n  }\n  if (queryState === QueryState.Stale) {\n    return 'stale'\n  }\n  if (queryState === QueryState.Inactive) {\n    return 'inactive'\n  }\n\n  return 'fresh'\n}\n\nexport function getQueryStatusFg(query: Query): number {\n  const queryState = getQueryState(query)\n\n  if (queryState === QueryState.Stale) {\n    return 0x000000\n  }\n\n  return 0xffffff\n}\n\nexport function getQueryStatusBg(query: Query): number {\n  const queryState = getQueryState(query)\n\n  if (queryState === QueryState.Fetching) {\n    return 0x006bff\n  }\n  if (queryState === QueryState.Paused) {\n    return 0x8c49eb\n  }\n  if (queryState === QueryState.Stale) {\n    return 0xffb200\n  }\n  if (queryState === QueryState.Inactive) {\n    return 0x3f4e60\n  }\n\n  return 0x008327\n}\n\nconst queryHashSort: SortFn = (a, b) => a.queryHash.localeCompare(b.queryHash)\n\nconst dateSort: SortFn = (a, b) =>\n  a.state.dataUpdatedAt < b.state.dataUpdatedAt ? 1 : -1\n\nconst statusAndDateSort: SortFn = (a, b) => {\n  if (getQueryState(a) === getQueryState(b)) {\n    return dateSort(a, b)\n  }\n\n  return getQueryState(a) > getQueryState(b) ? 1 : -1\n}\n\nexport const sortFns: Record<string, SortFn> = {\n  'Status > Last Updated': statusAndDateSort,\n  'Query Hash': queryHashSort,\n  'Last Updated': dateSort,\n}\n", "/* istanbul ignore file */\n\nimport { setupDevtoolsPlugin } from '@vue/devtools-api'\nimport { rankItem } from '@tanstack/match-sorter-utils'\nimport {\n  getQueryStateLabel,\n  getQueryStatusBg,\n  getQueryStatusFg,\n  sortFns,\n} from './utils'\nimport type { CustomInspectorNode } from '@vue/devtools-api'\nimport type { Query, QueryCacheNotifyEvent } from '@tanstack/query-core'\nimport type { QueryClient } from '../queryClient'\n\nconst pluginId = 'vue-query'\nconst pluginName = 'Vue Query'\n\nexport function setupDevtools(app: any, queryClient: QueryClient) {\n  setupDevtoolsPlugin(\n    {\n      id: pluginId,\n      label: pluginName,\n      packageName: 'vue-query',\n      homepage: 'https://tanstack.com/query/v4',\n      logo: 'https://vue-query.vercel.app/vue-query.svg',\n      app,\n      settings: {\n        baseSort: {\n          type: 'choice',\n          component: 'button-group',\n          label: 'Sort Cache Entries',\n          options: [\n            {\n              label: 'ASC',\n              value: 1,\n            },\n            {\n              label: 'DESC',\n              value: -1,\n            },\n          ],\n          defaultValue: 1,\n        },\n        sortFn: {\n          type: 'choice',\n          label: 'Sort Function',\n          options: Object.keys(sortFns).map((key) => ({\n            label: key,\n            value: key,\n          })),\n          defaultValue: Object.keys(sortFns)[0]!,\n        },\n      },\n    },\n    (api) => {\n      const queryCache = queryClient.getQueryCache()\n\n      api.addInspector({\n        id: pluginId,\n        label: pluginName,\n        icon: 'api',\n        nodeActions: [\n          {\n            icon: 'cloud_download',\n            tooltip: 'Refetch',\n            action: (queryHash: string) => {\n              queryCache.get(queryHash)?.fetch()\n            },\n          },\n          {\n            icon: 'alarm',\n            tooltip: 'Invalidate',\n            action: (queryHash: string) => {\n              const query = queryCache.get(queryHash) as Query\n              queryClient.invalidateQueries(query.queryKey)\n            },\n          },\n          {\n            icon: 'settings_backup_restore',\n            tooltip: 'Reset',\n            action: (queryHash: string) => {\n              queryCache.get(queryHash)?.reset()\n            },\n          },\n          {\n            icon: 'delete',\n            tooltip: 'Remove',\n            action: (queryHash: string) => {\n              const query = queryCache.get(queryHash) as Query\n              queryCache.remove(query)\n            },\n          },\n        ],\n      })\n\n      api.addTimelineLayer({\n        id: pluginId,\n        label: pluginName,\n        color: 0xffd94c,\n      })\n\n      queryCache.subscribe((event) => {\n        api.sendInspectorTree(pluginId)\n        api.sendInspectorState(pluginId)\n\n        const queryEvents: QueryCacheNotifyEvent['type'][] = [\n          'added',\n          'removed',\n          'updated',\n        ]\n        if (\n          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n          event &&\n          queryEvents.includes(event.type)\n        ) {\n          api.addTimelineEvent({\n            layerId: pluginId,\n            event: {\n              title: event.type,\n              subtitle: event.query.queryHash,\n              time: api.now(),\n              data: {\n                queryHash: event.query.queryHash,\n                ...event,\n              },\n            },\n          })\n        }\n      })\n\n      api.on.getInspectorTree((payload) => {\n        if (payload.inspectorId === pluginId) {\n          const queries = queryCache.getAll()\n          const settings = api.getSettings()\n\n          const filtered = payload.filter\n            ? queries.filter(\n                (item) => rankItem(item.queryHash, payload.filter).passed,\n              )\n            : [...queries]\n\n          const sorted = filtered.sort(\n            (a, b) => sortFns[settings.sortFn]!(a, b) * settings.baseSort,\n          )\n\n          const nodes: CustomInspectorNode[] = sorted.map((query) => {\n            const stateLabel = getQueryStateLabel(query)\n\n            return {\n              id: query.queryHash,\n              label: query.queryHash,\n              tags: [\n                {\n                  label: `${stateLabel} [${query.getObserversCount()}]`,\n                  textColor: getQueryStatusFg(query),\n                  backgroundColor: getQueryStatusBg(query),\n                },\n              ],\n            }\n          })\n          payload.rootNodes = nodes\n        }\n      })\n\n      api.on.getInspectorState((payload) => {\n        if (payload.inspectorId === pluginId) {\n          const query = queryCache.get(payload.nodeId)\n\n          if (!query) {\n            return\n          }\n\n          payload.state = {\n            ' Query Details': [\n              {\n                key: 'Query key',\n                value: query.queryHash as string,\n              },\n              {\n                key: 'Query status',\n                value: getQueryStateLabel(query),\n              },\n              {\n                key: 'Observers',\n                value: query.getObserversCount(),\n              },\n              {\n                key: 'Last Updated',\n                value: new Date(query.state.dataUpdatedAt).toLocaleTimeString(),\n              },\n            ],\n            'Data Explorer': [\n              {\n                key: 'Data',\n                value: query.state.data,\n              },\n            ],\n            'Query Explorer': [\n              {\n                key: 'Query',\n                value: query,\n              },\n            ],\n          }\n        }\n      })\n    },\n  )\n}\n", "import { isVue2 } from 'vue-demi'\nimport { isServer } from '@tanstack/query-core'\n\nimport { QueryClient } from './queryClient'\nimport { getClientKey } from './utils'\nimport { setupDevtools } from './devtools/devtools'\nimport type { QueryClientConfig } from '@tanstack/query-core'\nimport type { MaybeRefDeep } from './types'\n\ndeclare global {\n  interface Window {\n    __VUE_QUERY_CONTEXT__?: QueryClient\n  }\n}\n\ntype ClientPersister = (client: QueryClient) => [() => void, Promise<void>]\n\ninterface CommonOptions {\n  queryClientKey?: string\n  contextSharing?: boolean\n  clientPersister?: ClientPersister\n  clientPersisterOnSuccess?: (client: QueryClient) => void\n}\n\ninterface ConfigOptions extends CommonOptions {\n  queryClientConfig?: MaybeRefDeep<QueryClientConfig>\n}\n\ninterface ClientOptions extends CommonOptions {\n  queryClient?: QueryClient\n}\n\nexport type VueQueryPluginOptions = ConfigOptions | ClientOptions\n\nexport const VueQueryPlugin = {\n  install: (app: any, options: VueQueryPluginOptions = {}) => {\n    const clientKey = getClientKey(options.queryClientKey)\n    let client: QueryClient\n\n    if ('queryClient' in options && options.queryClient) {\n      client = options.queryClient\n    } else {\n      if (options.contextSharing && typeof window !== 'undefined') {\n        if (!window.__VUE_QUERY_CONTEXT__) {\n          const clientConfig =\n            'queryClientConfig' in options\n              ? options.queryClientConfig\n              : undefined\n          client = new QueryClient(clientConfig)\n          window.__VUE_QUERY_CONTEXT__ = client\n        } else {\n          client = window.__VUE_QUERY_CONTEXT__\n        }\n      } else {\n        const clientConfig =\n          'queryClientConfig' in options ? options.queryClientConfig : undefined\n        client = new QueryClient(clientConfig)\n      }\n    }\n\n    if (!isServer) {\n      client.mount()\n    }\n\n    let persisterUnmount = () => {\n      // noop\n    }\n\n    if (options.clientPersister) {\n      client.isRestoring.value = true\n      const [unmount, promise] = options.clientPersister(client)\n      persisterUnmount = unmount\n      promise.then(() => {\n        client.isRestoring.value = false\n        options.clientPersisterOnSuccess?.(client)\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'production' && options.contextSharing) {\n      client\n        .getLogger()\n        .error(\n          `The contextSharing option has been deprecated and will be removed in the next major version`,\n        )\n    }\n\n    const cleanup = () => {\n      client.unmount()\n      persisterUnmount()\n    }\n\n    if (app.onUnmount) {\n      app.onUnmount(cleanup)\n    } else {\n      const originalUnmount = app.unmount\n      app.unmount = function vueQueryUnmount() {\n        cleanup()\n        originalUnmount()\n      }\n    }\n\n    /* istanbul ignore next */\n    if (isVue2) {\n      app.mixin({\n        beforeCreate() {\n          // HACK: taken from provide(): https://github.com/vuejs/composition-api/blob/master/src/apis/inject.ts#L30\n          if (!this._provided) {\n            const provideCache = {}\n            Object.defineProperty(this, '_provided', {\n              get: () => provideCache,\n              set: (v) => Object.assign(provideCache, v),\n            })\n          }\n\n          this._provided[clientKey] = client\n\n          if (process.env.NODE_ENV === 'development') {\n            if (this === this.$root) {\n              setupDevtools(this, client)\n            }\n          }\n        },\n      })\n    } else {\n      app.provide(clientKey, client)\n\n      if (process.env.NODE_ENV === 'development') {\n        setupDevtools(app, client)\n      }\n    }\n  },\n}\n", "import {\n  computed,\n  getCurrentScope,\n  onScopeDispose,\n  reactive,\n  readonly,\n  toRefs,\n  unref,\n  watch,\n} from 'vue-demi'\nimport { useQueryClient } from './useQueryClient'\nimport {\n  cloneDeepUnref,\n  isQuery<PERSON>ey,\n  shouldThrowError,\n  updateState,\n} from './utils'\nimport type { ToRef } from 'vue-demi'\nimport type {\n  QueryFunction,\n  QueryKey,\n  QueryObserver,\n  QueryObserverOptions,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { DeepUnwrapRef, MaybeRef, WithQueryClientKey } from './types'\nimport type { UseQueryOptions } from './useQuery'\nimport type { UseInfiniteQueryOptions } from './useInfiniteQuery'\n\nexport type UseQueryReturnType<\n  TData,\n  TError,\n  Result = QueryObserverResult<TData, TError>,\n> = {\n  [K in keyof Result]: K extends\n    | 'fetchNextPage'\n    | 'fetchPreviousPage'\n    | 'refetch'\n    | 'remove'\n    ? Result[K]\n    : ToRef<Readonly<Result>[K]>\n} & {\n  suspense: () => Promise<Result>\n}\n\ntype UseQueryOptionsGeneric<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> =\n  | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  | UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  Observer: typeof QueryObserver,\n  arg1:\n    | MaybeRef<TQueryKey>\n    | MaybeRef<UseQueryOptionsGeneric<TQueryFnData, TError, TData, TQueryKey>>,\n  arg2:\n    | MaybeRef<QueryFunction<TQueryFnData, DeepUnwrapRef<TQueryKey>>>\n    | MaybeRef<\n        UseQueryOptionsGeneric<TQueryFnData, TError, TData, TQueryKey>\n      > = {},\n  arg3: MaybeRef<\n    UseQueryOptionsGeneric<TQueryFnData, TError, TData, TQueryKey>\n  > = {},\n): UseQueryReturnType<TData, TError> {\n  if (process.env.NODE_ENV === 'development') {\n    if (!getCurrentScope()) {\n      console.warn(\n        'vue-query composables like \"useQuery()\" should only be used inside a \"setup()\" function or a running effect scope. They might otherwise lead to memory leaks.',\n      )\n    }\n  }\n\n  const options = computed(() => parseQueryArgs(arg1, arg2, arg3))\n\n  const queryClient =\n    options.value.queryClient ?? useQueryClient(options.value.queryClientKey)\n\n  const defaultedOptions = computed(() => {\n    const defaulted = queryClient.defaultQueryOptions(options.value)\n    defaulted._optimisticResults = queryClient.isRestoring.value\n      ? 'isRestoring'\n      : 'optimistic'\n\n    return defaulted\n  })\n\n  const observer = new Observer(queryClient, defaultedOptions.value)\n  const state = reactive(observer.getCurrentResult())\n\n  let unsubscribe = () => {\n    // noop\n  }\n\n  watch(\n    queryClient.isRestoring,\n    (isRestoring) => {\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isRestoring) {\n        unsubscribe()\n        unsubscribe = observer.subscribe((result) => {\n          updateState(state, result)\n        })\n      }\n    },\n    { immediate: true },\n  )\n\n  const updater = () => {\n    observer.setOptions(defaultedOptions.value)\n    updateState(state, observer.getCurrentResult())\n  }\n\n  watch(defaultedOptions, updater)\n\n  onScopeDispose(() => {\n    unsubscribe()\n  })\n\n  // fix #5910\n  const refetch = (...args: Parameters<typeof state['refetch']>) => {\n    updater()\n    return state.refetch(...args)\n  }\n\n  const suspense = () => {\n    return new Promise<QueryObserverResult<TData, TError>>(\n      (resolve, reject) => {\n        let stopWatch = () => {\n          //noop\n        }\n        const run = () => {\n          if (defaultedOptions.value.enabled !== false) {\n            const optimisticResult = observer.getOptimisticResult(\n              defaultedOptions.value,\n            )\n            if (optimisticResult.isStale) {\n              stopWatch()\n              observer\n                .fetchOptimistic(defaultedOptions.value)\n                .then(resolve, reject)\n            } else {\n              stopWatch()\n              resolve(optimisticResult)\n            }\n          }\n        }\n\n        run()\n\n        stopWatch = watch(defaultedOptions, run)\n      },\n    )\n  }\n\n  // Handle error boundary\n  watch(\n    () => state.error,\n    (error) => {\n      if (\n        state.isError &&\n        !state.isFetching &&\n        shouldThrowError(defaultedOptions.value.useErrorBoundary, [\n          error as TError,\n          observer.getCurrentQuery(),\n        ])\n      ) {\n        throw error\n      }\n    },\n  )\n\n  const object: any = toRefs(readonly(state))\n  for (const key in state) {\n    if (typeof state[key as keyof typeof state] === 'function') {\n      object[key] = state[key as keyof typeof state]\n    }\n  }\n\n  object.suspense = suspense\n  object.refetch = refetch\n\n  return object as UseQueryReturnType<TData, TError>\n}\n\nexport function parseQueryArgs<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1:\n    | MaybeRef<TQueryKey>\n    | MaybeRef<UseQueryOptionsGeneric<TQueryFnData, TError, TData, TQueryKey>>,\n  arg2:\n    | MaybeRef<QueryFunction<TQueryFnData, DeepUnwrapRef<TQueryKey>>>\n    | MaybeRef<\n        UseQueryOptionsGeneric<TQueryFnData, TError, TData, TQueryKey>\n      > = {},\n  arg3: MaybeRef<\n    UseQueryOptionsGeneric<TQueryFnData, TError, TData, TQueryKey>\n  > = {},\n): WithQueryClientKey<\n  QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>\n> {\n  const plainArg1 = unref(arg1)\n  const plainArg2 = unref(arg2)\n  const plainArg3 = unref(arg3)\n\n  let options = plainArg1\n\n  if (!isQueryKey(plainArg1)) {\n    options = plainArg1\n  } else if (typeof plainArg2 === 'function') {\n    options = { ...plainArg3, queryKey: plainArg1, queryFn: plainArg2 }\n  } else {\n    options = { ...plainArg2, queryKey: plainArg1 }\n  }\n\n  const clondedOptions = cloneDeepUnref(options)\n\n  if (typeof clondedOptions.enabled === 'function') {\n    clondedOptions.enabled = clondedOptions.enabled()\n  }\n\n  return clondedOptions as WithQueryClientKey<\n    QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>\n  >\n}\n", "import { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefinedQueryObserverResult,\n  QueryFunction,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type { UseQueryReturnType as UQRT } from './useBaseQuery'\nimport type {\n  DeepUnwrapRef,\n  MaybeRef,\n  VueQueryObserverOptions,\n  WithQueryClientKey,\n} from './types'\n\nexport type UseQueryReturnType<TData, TError> = UQRT<TData, TError>\n\nexport type UseQueryDefinedReturnType<TData, TError> = UQRT<\n  TData,\n  TError,\n  DefinedQueryObserverResult<TData, TError>\n>\n\nexport type UseQueryOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = WithQueryClientKey<\n  VueQueryObserverOptions<TQueryFnData, TError, TData, TQueryFnData, TQueryKey>\n>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: MaybeRef<\n    Omit<\n      UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'initialData'\n    > & { initialData?: () => undefined }\n  >,\n): UseQueryReturnType<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: MaybeRef<\n    Omit<\n      UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'initialData'\n    > & { initialData: TQueryFnData | (() => TQueryFnData) }\n  >,\n): UseQueryDefinedReturnType<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: MaybeRef<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>>,\n): UseQueryReturnType<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: MaybeRef<TQueryKey>,\n  options?: MaybeRef<\n    Omit<\n      UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'initialData'\n    > & { initialData?: () => undefined }\n  >,\n): UseQueryReturnType<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: MaybeRef<TQueryKey>,\n  options?: MaybeRef<\n    Omit<\n      UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'initialData'\n    > & { initialData: TQueryFnData | (() => TQueryFnData) }\n  >,\n): UseQueryDefinedReturnType<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: MaybeRef<TQueryKey>,\n  options?: MaybeRef<\n    Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'queryKey'>\n  >,\n): UseQueryReturnType<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: MaybeRef<TQueryKey>,\n  queryFn: MaybeRef<QueryFunction<TQueryFnData, DeepUnwrapRef<TQueryKey>>>,\n  options?: MaybeRef<\n    Omit<\n      UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn' | 'initialData'\n    > & { initialData?: () => undefined }\n  >,\n): UseQueryReturnType<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: MaybeRef<TQueryKey>,\n  queryFn: MaybeRef<QueryFunction<TQueryFnData, DeepUnwrapRef<TQueryKey>>>,\n  options?: MaybeRef<\n    Omit<\n      UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn' | 'initialData'\n    > & { initialData: TQueryFnData | (() => TQueryFnData) }\n  >,\n): UseQueryDefinedReturnType<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: MaybeRef<TQueryKey>,\n  queryFn: MaybeRef<QueryFunction<TQueryFnData, DeepUnwrapRef<TQueryKey>>>,\n  options?: MaybeRef<\n    Omit<\n      UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >\n  >,\n): UseQueryReturnType<TData, TError>\n\nexport function useQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1:\n    | MaybeRef<TQueryKey>\n    | MaybeRef<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>>,\n  arg2?:\n    | MaybeRef<QueryFunction<TQueryFnData, DeepUnwrapRef<TQueryKey>>>\n    | MaybeRef<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>>,\n  arg3?: MaybeRef<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>>,\n):\n  | UseQueryReturnType<TData, TError>\n  | UseQueryDefinedReturnType<TData, TError> {\n  const result = useBaseQuery(QueryObserver, arg1, arg2, arg3)\n\n  return result\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { QueriesObserver } from '@tanstack/query-core'\nimport {\n  computed,\n  getCurrentScope,\n  onScopeDispose,\n  reactive,\n  readonly,\n  watch,\n} from 'vue-demi'\nimport { useQueryClient } from './useQueryClient'\nimport { cloneDeepUnref } from './utils'\nimport type { Ref } from 'vue-demi'\n\nimport type { QueryFunction, QueryObserverResult } from '@tanstack/query-core'\n\nimport type { UseQueryOptions } from './useQuery'\nimport type { QueryClient } from './queryClient'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryOptions<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryOptions<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseQueryOptions<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryOptions<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryOptions<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryOptions<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey> }\n    ? UseQueryOptions<TQueryFnData, unknown, TQueryFnData, TQueryKey>\n    : // Fallback\n      UseQueryOptions\n\ntype GetResults<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? QueryObserverResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? QueryObserverResult<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? QueryObserverResult<TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? QueryObserverResult<TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? QueryObserverResult<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? QueryObserverResult<TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<unknown, any>\n        select: (data: any) => infer TData\n      }\n    ? QueryObserverResult<TData>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, any> }\n    ? QueryObserverResult<TQueryFnData>\n    : // Fallback\n      QueryObserverResult\n\n/**\n * UseQueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type UseQueriesOptions<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = [],\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryOptions[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? UseQueriesOptions<[...Tail], [...Result, GetOptions<Head>], [...Depth, 1]>\n  : unknown[] extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends UseQueryOptions<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      infer TQueryKey\n    >[]\n  ? UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>[]\n  : // Fallback\n    UseQueryOptions[]\n\n/**\n * UseQueriesResults reducer recursively maps type param to results\n */\nexport type UseQueriesResults<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = [],\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? QueryObserverResult[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetResults<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? UseQueriesResults<[...Tail], [...Result, GetResults<Head>], [...Depth, 1]>\n  : T extends UseQueryOptions<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      any\n    >[]\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    QueryObserverResult<unknown extends TData ? TQueryFnData : TData, TError>[]\n  : // Fallback\n    QueryObserverResult[]\n\ntype UseQueriesOptionsArg<T extends any[]> = readonly [...UseQueriesOptions<T>]\n\nexport function useQueries<T extends any[]>({\n  queries,\n  queryClient: queryClientInjected,\n}: {\n  queries: Ref<UseQueriesOptionsArg<T>> | UseQueriesOptionsArg<T>\n  queryClient?: QueryClient\n}): Readonly<UseQueriesResults<T>> {\n  if (process.env.NODE_ENV === 'development') {\n    if (!getCurrentScope()) {\n      console.warn(\n        'vue-query composables like \"useQuery()\" should only be used inside a \"setup()\" function or a running effect scope. They might otherwise lead to memory leaks.',\n      )\n    }\n  }\n\n  const unreffedQueries = computed(() => {\n    const clonedQueries = cloneDeepUnref(queries)\n\n    ;(clonedQueries as any[]).map((query) => {\n      if (typeof query.enabled === 'function') {\n        query.enabled = query.enabled()\n      }\n    })\n\n    return clonedQueries as UseQueriesOptionsArg<T>\n  })\n\n  const queryClientKey = unreffedQueries.value[0]?.queryClientKey\n  const optionsQueryClient = unreffedQueries.value[0]?.queryClient as\n    | QueryClient\n    | undefined\n  const queryClient =\n    queryClientInjected ?? optionsQueryClient ?? useQueryClient(queryClientKey)\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    (queryClientKey || optionsQueryClient)\n  ) {\n    queryClient\n      .getLogger()\n      .error(\n        `Providing queryClient to individual queries in useQueries has been deprecated and will be removed in the next major version. You can still pass queryClient as an option directly to useQueries hook.`,\n      )\n  }\n\n  const defaultedQueries = computed(() =>\n    unreffedQueries.value.map((options) => {\n      const defaulted = queryClient.defaultQueryOptions(options)\n      defaulted._optimisticResults = queryClient.isRestoring.value\n        ? 'isRestoring'\n        : 'optimistic'\n\n      return defaulted\n    }),\n  )\n\n  const observer = new QueriesObserver(queryClient, defaultedQueries.value)\n  const state = reactive(observer.getCurrentResult())\n\n  let unsubscribe = () => {\n    // noop\n  }\n\n  watch(\n    queryClient.isRestoring,\n    (isRestoring) => {\n      if (!isRestoring) {\n        unsubscribe()\n        unsubscribe = observer.subscribe((result) => {\n          state.splice(0, result.length, ...result)\n        })\n        // Subscription would not fire for persisted results\n        state.splice(\n          0,\n          state.length,\n          ...observer.getOptimisticResult(defaultedQueries.value),\n        )\n      }\n    },\n    { immediate: true },\n  )\n\n  watch(\n    defaultedQueries,\n    () => {\n      observer.setQueries(defaultedQueries.value)\n      state.splice(0, state.length, ...observer.getCurrentResult())\n    },\n    { flush: 'sync' },\n  )\n\n  onScopeDispose(() => {\n    unsubscribe()\n  })\n\n  return readonly(state) as UseQueriesResults<T>\n}\n", "import { InfiniteQueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  InfiniteQueryObserverResult,\n  QueryFunction,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\n\nimport type { UseQueryReturnType } from './useBaseQuery'\n\nimport type {\n  DeepUnwrapRef,\n  VueInfiniteQueryObserverOptions,\n  WithQueryClientKey,\n} from './types'\n\nexport type UseInfiniteQueryOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = WithQueryClientKey<\n  VueInfiniteQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >\n>\n\nexport type UseInfiniteQueryReturnType<TData, TError> = UseQueryReturnType<\n  TData,\n  TError,\n  InfiniteQueryObserverResult<TData, TError>\n>\n\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseInfiniteQueryReturnType<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey'\n  >,\n): UseInfiniteQueryReturnType<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, DeepUnwrapRef<TQueryKey>>,\n  options?: Omit<\n    UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn'\n  >,\n): UseInfiniteQueryReturnType<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1:\n    | TQueryKey\n    | UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg2?:\n    | QueryFunction<TQueryFnData, DeepUnwrapRef<TQueryKey>>\n    | UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg3?: UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseInfiniteQueryReturnType<TData, TError> {\n  const result = useBaseQuery(\n    InfiniteQueryObserver as typeof QueryObserver,\n    arg1,\n    arg2,\n    arg3,\n  ) as UseInfiniteQueryReturnType<TData, TError>\n\n  return result\n}\n", "import {\n  computed,\n  getCurrentScope,\n  onScopeDispose,\n  reactive,\n  readonly,\n  toRefs,\n  unref,\n  watch,\n} from 'vue-demi'\nimport { MutationObserver } from '@tanstack/query-core'\nimport {\n  cloneDeepUnref,\n  isMutationKey,\n  shouldThrowError,\n  updateState,\n} from './utils'\nimport { useQueryClient } from './useQueryClient'\nimport type { ToRefs } from 'vue-demi'\nimport type {\n  DistributiveOmit,\n  MutateFunction,\n  MutateOptions,\n  MutationFunction,\n  MutationKey,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from '@tanstack/query-core'\nimport type { MaybeRef, MaybeRefDeep, WithQueryClientKey } from './types'\n\ntype MutationResult<TData, TError, TVariables, TContext> = DistributiveOmit<\n  MutationObserverResult<TData, TError, TVariables, TContext>,\n  'mutate' | 'reset'\n>\n\nexport type UseMutationOptions<TData, TError, TVariables, TContext> =\n  WithQueryClientKey<\n    MutationObserverOptions<TData, TError, TVariables, TContext>\n  >\n\nexport type VueMutationObserverOptions<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> = {\n  [Property in keyof UseMutationOptions<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >]: MaybeRefDeep<\n    UseMutationOptions<TData, TError, TVariables, TContext>[Property]\n  >\n}\n\ntype MutateSyncFunction<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> = (\n  ...options: Parameters<MutateFunction<TData, TError, TVariables, TContext>>\n) => void\n\nexport type UseMutationReturnType<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n  Result = MutationResult<TData, TError, TVariables, TContext>,\n> = ToRefs<Readonly<Result>> & {\n  mutate: MutateSyncFunction<TData, TError, TVariables, TContext>\n  mutateAsync: MutateFunction<TData, TError, TVariables, TContext>\n  reset: MutationObserverResult<TData, TError, TVariables, TContext>['reset']\n}\n\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: MaybeRef<\n    VueMutationObserverOptions<TData, TError, TVariables, TContext>\n  >,\n): UseMutationReturnType<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationFn: MaybeRef<MutationFunction<TData, TVariables>>,\n  options?: MaybeRef<\n    Omit<\n      VueMutationObserverOptions<TData, TError, TVariables, TContext>,\n      'mutationFn'\n    >\n  >,\n): UseMutationReturnType<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MaybeRef<MutationKey>,\n  options?: MaybeRef<\n    Omit<\n      VueMutationObserverOptions<TData, TError, TVariables, TContext>,\n      'mutationKey'\n    >\n  >,\n): UseMutationReturnType<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MaybeRef<MutationKey>,\n  mutationFn?: MaybeRef<MutationFunction<TData, TVariables>>,\n  options?: MaybeRef<\n    Omit<\n      VueMutationObserverOptions<TData, TError, TVariables, TContext>,\n      'mutationKey' | 'mutationFn'\n    >\n  >,\n): UseMutationReturnType<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  arg1:\n    | MaybeRef<MutationKey>\n    | MaybeRef<MutationFunction<TData, TVariables>>\n    | MaybeRef<VueMutationObserverOptions<TData, TError, TVariables, TContext>>,\n  arg2?:\n    | MaybeRef<MutationFunction<TData, TVariables>>\n    | MaybeRef<VueMutationObserverOptions<TData, TError, TVariables, TContext>>,\n  arg3?: MaybeRef<\n    VueMutationObserverOptions<TData, TError, TVariables, TContext>\n  >,\n): UseMutationReturnType<TData, TError, TVariables, TContext> {\n  if (process.env.NODE_ENV === 'development') {\n    if (!getCurrentScope()) {\n      console.warn(\n        'vue-query composables like \"useQuery()\" should only be used inside a \"setup()\" function or a running effect scope. They might otherwise lead to memory leaks.',\n      )\n    }\n  }\n\n  const options = computed(() => {\n    return parseMutationArgs(arg1, arg2, arg3)\n  })\n  const queryClient =\n    options.value.queryClient ?? useQueryClient(options.value.queryClientKey)\n  const observer = new MutationObserver(\n    queryClient,\n    queryClient.defaultMutationOptions(options.value),\n  )\n  const state = reactive(observer.getCurrentResult())\n\n  const unsubscribe = observer.subscribe((result) => {\n    updateState(state, result)\n  })\n\n  const mutate = (\n    variables: TVariables,\n    mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>,\n  ) => {\n    observer.mutate(variables, mutateOptions).catch(() => {\n      // This is intentional\n    })\n  }\n\n  watch(options, () => {\n    observer.setOptions(queryClient.defaultMutationOptions(options.value))\n  })\n\n  onScopeDispose(() => {\n    unsubscribe()\n  })\n\n  const resultRefs = toRefs(readonly(state)) as unknown as ToRefs<\n    Readonly<MutationResult<TData, TError, TVariables, TContext>>\n  >\n\n  watch(\n    () => state.error,\n    (error) => {\n      if (\n        error &&\n        shouldThrowError(options.value.useErrorBoundary, [error as TError])\n      ) {\n        throw error\n      }\n    },\n  )\n\n  return {\n    ...resultRefs,\n    mutate,\n    mutateAsync: state.mutate,\n    reset: state.reset,\n  }\n}\n\nexport function parseMutationArgs<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  arg1:\n    | MaybeRef<MutationKey>\n    | MaybeRef<MutationFunction<TData, TVariables>>\n    | MaybeRef<VueMutationObserverOptions<TData, TError, TVariables, TContext>>,\n  arg2?:\n    | MaybeRef<MutationFunction<TData, TVariables>>\n    | MaybeRef<VueMutationObserverOptions<TData, TError, TVariables, TContext>>,\n  arg3?: MaybeRef<\n    VueMutationObserverOptions<TData, TError, TVariables, TContext>\n  >,\n): WithQueryClientKey<\n  MutationObserverOptions<TData, TError, TVariables, TContext>\n> {\n  const plainArg1 = unref(arg1)\n  const plainArg2 = unref(arg2)\n  let options = plainArg1\n  if (isMutationKey(plainArg1)) {\n    if (typeof plainArg2 === 'function') {\n      const plainArg3 = unref(arg3)\n      options = { ...plainArg3, mutationKey: plainArg1, mutationFn: plainArg2 }\n    } else {\n      options = { ...plainArg2, mutationKey: plainArg1 }\n    }\n  }\n\n  if (typeof plainArg1 === 'function') {\n    options = { ...plainArg2, mutationFn: plainArg1 }\n  }\n\n  return cloneDeepUnref(options) as UseMutationOptions<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n}\n", "import {\n  computed,\n  getCurrentScope,\n  onScopeDispose,\n  ref,\n  unref,\n  watchEffect,\n} from 'vue-demi'\nimport { useQueryClient } from './useQueryClient'\nimport { cloneDeepUnref, isQuery<PERSON>ey } from './utils'\nimport type { Ref } from 'vue-demi'\nimport type { QueryFilters as QF, QueryKey } from '@tanstack/query-core'\n\nimport type { MaybeRef, MaybeRefDeep, WithQueryClientKey } from './types'\n\nexport type QueryFilters = MaybeRefDeep<WithQueryClientKey<QF>>\n\nexport function useIsFetching(filters?: QueryFilters): Ref<number>\nexport function useIsFetching(\n  queryKey?: MaybeRef<QueryKey>,\n  filters?: Omit<QueryFilters, 'queryKey'>,\n): Ref<number>\nexport function useIsFetching(\n  arg1?: MaybeRef<QueryKey> | QueryFilters,\n  arg2?: Omit<QueryFilters, 'queryKey'>,\n): Ref<number> {\n  if (process.env.NODE_ENV === 'development') {\n    if (!getCurrentScope()) {\n      console.warn(\n        'vue-query composables like \"useQuery()\" should only be used inside a \"setup()\" function or a running effect scope. They might otherwise lead to memory leaks.',\n      )\n    }\n  }\n\n  const filters = computed(() => parseFilterArgs(arg1, arg2))\n  const queryClient =\n    filters.value.queryClient ?? useQueryClient(filters.value.queryClientKey)\n\n  const isFetching = ref()\n\n  const listener = () => {\n    isFetching.value = queryClient.isFetching(filters)\n  }\n\n  const unsubscribe = queryClient.getQueryCache().subscribe(listener)\n\n  watchEffect(listener)\n\n  onScopeDispose(() => {\n    unsubscribe()\n  })\n\n  return isFetching\n}\n\nexport function parseFilterArgs(\n  arg1?: MaybeRef<QueryKey> | QueryFilters,\n  arg2: QueryFilters = {},\n) {\n  const plainArg1 = unref(arg1)\n  const plainArg2 = unref(arg2)\n\n  let options = plainArg1\n\n  if (isQueryKey(plainArg1)) {\n    options = { ...plainArg2, queryKey: plainArg1 }\n  } else {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    options = plainArg1 || {}\n  }\n\n  return cloneDeepUnref(options) as WithQueryClientKey<QF>\n}\n", "import {\n  computed,\n  getCurrentScope,\n  onScopeDispose,\n  ref,\n  unref,\n  watchEffect,\n} from 'vue-demi'\nimport { useQueryClient } from './useQueryClient'\nimport { cloneDeepUnref, isQuery<PERSON>ey } from './utils'\nimport type { Ref } from 'vue-demi'\nimport type { MutationFilters as MF, MutationKey } from '@tanstack/query-core'\n\nimport type { MaybeRef, MaybeRefDeep, WithQueryClientKey } from './types'\n\nexport type MutationFilters = MaybeRefDeep<WithQueryClientKey<MF>>\n\nexport function useIsMutating(filters?: MutationFilters): Ref<number>\nexport function useIsMutating(\n  mutationKey?: MaybeRef<MutationKey>,\n  filters?: Omit<MutationFilters, 'mutationKey'>,\n): Ref<number>\nexport function useIsMutating(\n  arg1?: MaybeRef<MutationKey> | MutationFilters,\n  arg2?: Omit<MutationFilters, 'mutationKey'>,\n): Ref<number> {\n  if (process.env.NODE_ENV === 'development') {\n    if (!getCurrentScope()) {\n      console.warn(\n        'vue-query composables like \"useQuery()\" should only be used inside a \"setup()\" function or a running effect scope. They might otherwise lead to memory leaks.',\n      )\n    }\n  }\n\n  const filters = computed(() => parseFilterArgs(arg1, arg2))\n  const queryClient =\n    filters.value.queryClient ?? useQueryClient(filters.value.queryClientKey)\n\n  const isMutating = ref()\n\n  const listener = () => {\n    isMutating.value = queryClient.isMutating(filters)\n  }\n\n  const unsubscribe = queryClient.getMutationCache().subscribe(listener)\n\n  watchEffect(listener)\n\n  onScopeDispose(() => {\n    unsubscribe()\n  })\n\n  return isMutating\n}\n\nexport function parseFilterArgs(\n  arg1?: MaybeRef<MutationKey> | MutationFilters,\n  arg2: MutationFilters = {},\n) {\n  const plainArg1 = unref(arg1)\n  const plainArg2 = unref(arg2)\n\n  let options = plainArg1\n\n  if (isQueryKey(plainArg1)) {\n    options = { ...plainArg2, mutationKey: plainArg1 }\n  } else {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    options = plainArg1 || {}\n  }\n\n  return cloneDeepUnref(options) as WithQueryClientKey<MF>\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAMA,eAAN,MAA0D;EAG/DC,cAAc;AACZ,SAAKC,YAAY,oBAAIC,IAAJ;AACjB,SAAKC,YAAY,KAAKA,UAAUC,KAAK,IAApB;EAClB;EAEDD,UAAUE,UAAiC;AACzC,UAAMC,WAAW;MAAED;;AACnB,SAAKJ,UAAUM,IAAID,QAAnB;AAEA,SAAKE,YAAL;AAEA,WAAO,MAAM;AACX,WAAKP,UAAUQ,OAAOH,QAAtB;AACA,WAAKI,cAAL;;EAEH;EAEDC,eAAwB;AACtB,WAAO,KAAKV,UAAUW,OAAO;EAC9B;EAESJ,cAAoB;EAE7B;EAESE,gBAAsB;EAE/B;AA9B8D;;;ACoE1D,IAAMG,WAAW,OAAOC,WAAW,eAAe,UAAUA;AAE5D,SAASC,OAAkB;AAChC,SAAOC;AACR;AAEM,SAASC,iBACdC,SACAC,OACS;AACT,SAAO,OAAOD,YAAY,aACrBA,QAAgDC,KAAjD,IACAD;AACL;AAEM,SAASE,eAAeC,OAAiC;AAC9D,SAAO,OAAOA,UAAU,YAAYA,SAAS,KAAKA,UAAUC;AAC7D;AAEM,SAASC,WAAcC,QAAaC,QAAkB;AAC3D,SAAOD,OAAOE,OAAQC,OAAM,CAACF,OAAOG,SAASD,CAAhB,CAAtB;AACR;AAEM,SAASE,UAAaC,OAAYC,OAAeV,OAAe;AACrE,QAAMW,OAAOF,MAAMG,MAAM,CAAZ;AACbD,OAAKD,KAAD,IAAUV;AACd,SAAOW;AACR;AAEM,SAASE,eAAeC,WAAmBC,WAA4B;AAC5E,SAAOC,KAAKC,IAAIH,aAAaC,aAAa,KAAKG,KAAKC,IAAL,GAAY,CAApD;AACR;AAEM,SAASC,eAIdC,MACAC,MACAC,MACU;AACV,MAAI,CAACC,WAAWH,IAAD,GAAQ;AACrB,WAAOA;EACR;AAED,MAAI,OAAOC,SAAS,YAAY;AAC9B,WAAO,iCAAKC,OAAL;MAAWE,UAAUJ;MAAMK,SAASJ;;EAC5C;AAED,SAAO,iCAAKA,OAAL;IAAWG,UAAUJ;;AAC7B;AAEM,SAASM,kBAGdN,MACAC,MACAC,MACU;AACV,MAAIC,WAAWH,IAAD,GAAQ;AACpB,QAAI,OAAOC,SAAS,YAAY;AAC9B,aAAO,iCAAKC,OAAL;QAAWK,aAAaP;QAAMQ,YAAYP;;IAClD;AACD,WAAO,iCAAKA,OAAL;MAAWM,aAAaP;;EAChC;AAED,MAAI,OAAOA,SAAS,YAAY;AAC9B,WAAO,iCAAKC,OAAL;MAAWO,YAAYR;;EAC/B;AAED,SAAO,mBAAKA;AACb;AAEM,SAASS,gBAIdT,MACAC,MACAC,MACkC;AAClC,SACEC,WAAWH,IAAD,IAAS,CAAC,iCAAKC,OAAL;IAAWG,UAAUJ;MAAQE,IAA9B,IAAsC,CAACF,QAAQ,CAAA,GAAIC,IAAb;AAE5D;AAEM,SAASS,wBAIdV,MACAC,MACAC,MACkC;AAClC,SACEC,WAAWH,IAAD,IACN,CAAC,iCAAKC,OAAL;IAAWM,aAAaP;MAAQE,IAAjC,IACA,CAACF,QAAQ,CAAA,GAAIC,IAAb;AAEP;AAEM,SAASU,WACdC,SACAC,OACS;AACT,QAAM;IACJC,OAAO;IACPC;IACAC;IACAC;IACAb;IACAc;EANI,IAOFN;AAEJ,MAAIT,WAAWC,QAAD,GAAY;AACxB,QAAIW,OAAO;AACT,UAAIF,MAAMM,cAAcC,sBAAsBhB,UAAUS,MAAMQ,OAAjB,GAA2B;AACtE,eAAO;MACR;eACQ,CAACC,gBAAgBT,MAAMT,UAAUA,QAAjB,GAA4B;AACrD,aAAO;IACR;EACF;AAED,MAAIU,SAAS,OAAO;AAClB,UAAMS,WAAWV,MAAMU,SAAN;AACjB,QAAIT,SAAS,YAAY,CAACS,UAAU;AAClC,aAAO;IACR;AACD,QAAIT,SAAS,cAAcS,UAAU;AACnC,aAAO;IACR;EACF;AAED,MAAI,OAAOL,UAAU,aAAaL,MAAMW,QAAN,MAAoBN,OAAO;AAC3D,WAAO;EACR;AAED,MACE,OAAOF,gBAAgB,eACvBA,gBAAgBH,MAAMY,MAAMT,aAC5B;AACA,WAAO;EACR;AAED,MAAIC,aAAa,CAACA,UAAUJ,KAAD,GAAS;AAClC,WAAO;EACR;AAED,SAAO;AACR;AAEM,SAASa,cACdd,SACAe,UACS;AACT,QAAM;IAAEZ;IAAOa;IAAUX;IAAWV;EAA9B,IAA8CK;AACpD,MAAIT,WAAWI,WAAD,GAAe;AAC3B,QAAI,CAACoB,SAASN,QAAQd,aAAa;AACjC,aAAO;IACR;AACD,QAAIQ,OAAO;AACT,UACEc,aAAaF,SAASN,QAAQd,WAAlB,MAAmCsB,aAAatB,WAAD,GAC3D;AACA,eAAO;MACR;IACF,WAAU,CAACe,gBAAgBK,SAASN,QAAQd,aAAaA,WAA/B,GAA6C;AACtE,aAAO;IACR;EACF;AAED,MACE,OAAOqB,aAAa,aACnBD,SAASF,MAAMK,WAAW,cAAeF,UAC1C;AACA,WAAO;EACR;AAED,MAAIX,aAAa,CAACA,UAAUU,QAAD,GAAY;AACrC,WAAO;EACR;AAED,SAAO;AACR;AAEM,SAASP,sBACdhB,UACAiB,SACQ;AACR,QAAMU,UAASV,WAAO,OAAP,SAAAA,QAASW,mBAAkBH;AAC1C,SAAOE,OAAO3B,QAAD;AACd;AAMM,SAASyB,aAAazB,UAA4B;AACvD,SAAO6B,KAAKC,UAAU9B,UAAU,CAAC+B,GAAGC,QAClCC,cAAcD,GAAD,IACTE,OAAOC,KAAKH,GAAZ,EACGI,KADH,EAEGC,OAAO,CAACC,QAAQC,QAAQ;AACvBD,WAAOC,GAAD,IAAQP,IAAIO,GAAD;AACjB,WAAOD;EACR,GAAE,CAAA,CALL,IAMAN,GARC;AAUR;AAKM,SAASd,gBAAgBsB,GAAaC,GAAsB;AACjE,SAAOC,iBAAiBF,GAAGC,CAAJ;AACxB;AAKM,SAASC,iBAAiBF,GAAQC,GAAiB;AACxD,MAAID,MAAMC,GAAG;AACX,WAAO;EACR;AAED,MAAI,OAAOD,MAAM,OAAOC,GAAG;AACzB,WAAO;EACR;AAED,MAAID,KAAKC,KAAK,OAAOD,MAAM,YAAY,OAAOC,MAAM,UAAU;AAC5D,WAAO,CAACP,OAAOC,KAAKM,CAAZ,EAAeE,KAAMJ,SAAQ,CAACG,iBAAiBF,EAAED,GAAD,GAAOE,EAAEF,GAAD,CAAV,CAA9C;EACT;AAED,SAAO;AACR;AAQM,SAASK,iBAAiBJ,GAAQC,GAAa;AACpD,MAAID,MAAMC,GAAG;AACX,WAAOD;EACR;AAED,QAAMxD,QAAQ6D,aAAaL,CAAD,KAAOK,aAAaJ,CAAD;AAE7C,MAAIzD,SAAUiD,cAAcO,CAAD,KAAOP,cAAcQ,CAAD,GAAM;AACnD,UAAMK,QAAQ9D,QAAQwD,EAAEO,SAASb,OAAOC,KAAKK,CAAZ,EAAeO;AAChD,UAAMC,SAAShE,QAAQyD,IAAIP,OAAOC,KAAKM,CAAZ;AAC3B,UAAMQ,QAAQD,OAAOD;AACrB,UAAM7D,OAAYF,QAAQ,CAAA,IAAK,CAAA;AAE/B,QAAIkE,aAAa;AAEjB,aAASC,IAAI,GAAGA,IAAIF,OAAOE,KAAK;AAC9B,YAAMZ,MAAMvD,QAAQmE,IAAIH,OAAOG,CAAD;AAC9BjE,WAAKqD,GAAD,IAAQK,iBAAiBJ,EAAED,GAAD,GAAOE,EAAEF,GAAD,CAAV;AAC5B,UAAIrD,KAAKqD,GAAD,MAAUC,EAAED,GAAD,GAAO;AACxBW;MACD;IACF;AAED,WAAOJ,UAAUG,SAASC,eAAeJ,QAAQN,IAAItD;EACtD;AAED,SAAOuD;AACR;AAKM,SAASW,oBAAuBZ,GAAMC,GAAe;AAC1D,MAAKD,KAAK,CAACC,KAAOA,KAAK,CAACD,GAAI;AAC1B,WAAO;EACR;AAED,aAAWD,OAAOC,GAAG;AACnB,QAAIA,EAAED,GAAD,MAAUE,EAAEF,GAAD,GAAO;AACrB,aAAO;IACR;EACF;AAED,SAAO;AACR;AAEM,SAASM,aAAatE,OAAgB;AAC3C,SAAO8E,MAAMC,QAAQ/E,KAAd,KAAwBA,MAAMwE,WAAWb,OAAOC,KAAK5D,KAAZ,EAAmBwE;AACpE;AAGM,SAASd,cAAcsB,GAAqB;AACjD,MAAI,CAACC,mBAAmBD,CAAD,GAAK;AAC1B,WAAO;EACR;AAGD,QAAME,OAAOF,EAAEG;AACf,MAAI,OAAOD,SAAS,aAAa;AAC/B,WAAO;EACR;AAGD,QAAME,OAAOF,KAAKG;AAClB,MAAI,CAACJ,mBAAmBG,IAAD,GAAQ;AAC7B,WAAO;EACR;AAGD,MAAI,CAACA,KAAKE,eAAe,eAApB,GAAsC;AACzC,WAAO;EACR;AAGD,SAAO;AACR;AAED,SAASL,mBAAmBD,GAAiB;AAC3C,SAAOrB,OAAO0B,UAAUE,SAASC,KAAKR,CAA/B,MAAsC;AAC9C;AAEM,SAASxD,WAAWxB,OAAmC;AAC5D,SAAO8E,MAAMC,QAAQ/E,KAAd;AACR;AAEM,SAASyF,QAAQzF,OAA4B;AAClD,SAAOA,iBAAiB0F;AACzB;AAEM,SAASC,MAAMC,SAAgC;AACpD,SAAO,IAAIC,QAASC,aAAY;AAC9BC,eAAWD,SAASF,OAAV;EACX,CAFM;AAGR;AAMM,SAASI,kBAAkBC,UAAsB;AACtDN,QAAM,CAAD,EAAIO,KAAKD,QAAd;AACD;AAEM,SAASE,qBAAkD;AAChE,MAAI,OAAOC,oBAAoB,YAAY;AACzC,WAAO,IAAIA,gBAAJ;EACR;AACD;AACD;AAEM,SAASC,YAGdC,UAA6BC,MAAa7D,SAA0B;AAEpE,MAAIA,QAAQ8D,eAAZ,QAAI9D,QAAQ8D,YAAcF,UAAUC,IAAhC,GAAuC;AACzC,WAAOD;aACE,OAAO5D,QAAQ+D,sBAAsB,YAAY;AAC1D,WAAO/D,QAAQ+D,kBAAkBH,UAAUC,IAApC;EACR,WAAU7D,QAAQ+D,sBAAsB,OAAO;AAE9C,WAAOpC,iBAAiBiC,UAAUC,IAAX;EACxB;AACD,SAAOA;AACR;;;AC9aM,IAAMG,eAAN,cAA2BC,aAAa;EAM7CC,cAAc;AACZ,UAAA;AACA,SAAKC,QAASC,aAAY;AAGxB,UAAI,CAACC,YAAYC,OAAOC,kBAAkB;AACxC,cAAMC,WAAW,MAAMJ,QAAO;AAE9BE,eAAOC,iBAAiB,oBAAoBC,UAAU,KAAtD;AACAF,eAAOC,iBAAiB,SAASC,UAAU,KAA3C;AAEA,eAAO,MAAM;AAEXF,iBAAOG,oBAAoB,oBAAoBD,QAA/C;AACAF,iBAAOG,oBAAoB,SAASD,QAApC;;MAEH;AACD;;EAEH;EAESE,cAAoB;AAC5B,QAAI,CAAC,KAAKC,SAAS;AACjB,WAAKC,iBAAiB,KAAKT,KAA3B;IACD;EACF;EAESU,gBAAgB;AACxB,QAAI,CAAC,KAAKC,aAAL,GAAqB;AAAA,UAAA;AACxB,OAAA,gBAAA,KAAKH,YAAL,OAAA,SAAA,cAAA,KAAA,IAAA;AACA,WAAKA,UAAUI;IAChB;EACF;EAEDH,iBAAiBT,OAAsB;AAAA,QAAA;AACrC,SAAKA,QAAQA;AACb,KAAA,iBAAA,KAAKQ,YAAL,OAAA,SAAA,eAAA,KAAA,IAAA;AACA,SAAKA,UAAUR,MAAOa,aAAY;AAChC,UAAI,OAAOA,YAAY,WAAW;AAChC,aAAKC,WAAWD,OAAhB;MACD,OAAM;AACL,aAAKZ,QAAL;MACD;IACF,CANmB;EAOrB;EAEDa,WAAWD,SAAyB;AAClC,UAAME,UAAU,KAAKF,YAAYA;AACjC,QAAIE,SAAS;AACX,WAAKF,UAAUA;AACf,WAAKZ,QAAL;IACD;EACF;EAEDA,UAAgB;AACd,SAAKe,UAAUC,QAAQ,CAAC;MAAEZ;IAAF,MAAiB;AACvCA,eAAQ;KADV;EAGD;EAEDa,YAAqB;AACnB,QAAI,OAAO,KAAKL,YAAY,WAAW;AACrC,aAAO,KAAKA;IACb;AAGD,QAAI,OAAOM,aAAa,aAAa;AACnC,aAAO;IACR;AAED,WAAO,CAACP,QAAW,WAAW,WAAvB,EAAoCQ,SACzCD,SAASE,eADJ;EAGR;AA/E4C;AAkFlCC,IAAAA,eAAe,IAAIzB,aAAJ;;;AClF5B,IAAM0B,eAAe,CAAC,UAAU,SAAX;AAEd,IAAMC,gBAAN,cAA4BC,aAAa;EAM9CC,cAAc;AACZ,UAAA;AACA,SAAKC,QAASC,cAAa;AAGzB,UAAI,CAACC,YAAYC,OAAOC,kBAAkB;AACxC,cAAMC,WAAW,MAAMJ,SAAQ;AAE/BL,qBAAaU,QAASC,WAAU;AAC9BJ,iBAAOC,iBAAiBG,OAAOF,UAAU,KAAzC;SADF;AAIA,eAAO,MAAM;AAEXT,uBAAaU,QAASC,WAAU;AAC9BJ,mBAAOK,oBAAoBD,OAAOF,QAAlC;WADF;;MAIH;AAED;;EAEH;EAESI,cAAoB;AAC5B,QAAI,CAAC,KAAKC,SAAS;AACjB,WAAKC,iBAAiB,KAAKX,KAA3B;IACD;EACF;EAESY,gBAAgB;AACxB,QAAI,CAAC,KAAKC,aAAL,GAAqB;AAAA,UAAA;AACxB,OAAA,gBAAA,KAAKH,YAAL,OAAA,SAAA,cAAA,KAAA,IAAA;AACA,WAAKA,UAAUI;IAChB;EACF;EAEDH,iBAAiBX,OAAsB;AAAA,QAAA;AACrC,SAAKA,QAAQA;AACb,KAAA,iBAAA,KAAKU,YAAL,OAAA,SAAA,eAAA,KAAA,IAAA;AACA,SAAKA,UAAUV,MAAOe,YAAqB;AACzC,UAAI,OAAOA,WAAW,WAAW;AAC/B,aAAKC,UAAUD,MAAf;MACD,OAAM;AACL,aAAKd,SAAL;MACD;IACF,CANmB;EAOrB;EAEDe,UAAUD,QAAwB;AAChC,UAAME,UAAU,KAAKF,WAAWA;AAEhC,QAAIE,SAAS;AACX,WAAKF,SAASA;AACd,WAAKd,SAAL;IACD;EACF;EAEDA,WAAiB;AACf,SAAKiB,UAAUZ,QAAQ,CAAC;MAAED;IAAF,MAAiB;AACvCA,eAAQ;KADV;EAGD;EAEDc,WAAoB;AAClB,QAAI,OAAO,KAAKJ,WAAW,WAAW;AACpC,aAAO,KAAKA;IACb;AAED,QACE,OAAOK,cAAc,eACrB,OAAOA,UAAUC,WAAW,aAC5B;AACA,aAAO;IACR;AAED,WAAOD,UAAUC;EAClB;AAnF6C;AAsFnCC,IAAAA,gBAAgB,IAAIzB,cAAJ;;;ACrD7B,SAAS0B,kBAAkBC,cAAsB;AAC/C,SAAOC,KAAKC,IAAI,MAAO,SAAKF,eAAc,GAAnC;AACR;AAEM,SAASG,SAASC,aAA+C;AACtE,UAAQA,eAAAA,OAAAA,cAAe,cAAc,WACjCC,cAAcC,SAAd,IACA;AACL;AAEM,IAAMC,iBAAN,MAAqB;EAG1BC,YAAYC,SAAyB;AACnC,SAAKC,SAASD,WAAAA,OAAAA,SAAAA,QAASC;AACvB,SAAKC,SAASF,WAAAA,OAAAA,SAAAA,QAASE;EACxB;AANyB;AASrB,SAASC,iBAAiBC,OAAqC;AACpE,SAAOA,iBAAiBN;AACzB;AAEM,SAASO,cACdC,QACgB;AAChB,MAAIC,mBAAmB;AACvB,MAAIhB,eAAe;AACnB,MAAIiB,aAAa;AACjB,MAAIC;AACJ,MAAIC;AACJ,MAAIC;AAEJ,QAAMC,UAAU,IAAIC,QAAe,CAACC,cAAcC,gBAAgB;AAChEL,qBAAiBI;AACjBH,oBAAgBI;EACjB,CAHe;AAKhB,QAAMC,SAAUC,mBAAwC;AACtD,QAAI,CAACT,YAAY;AACfU,aAAO,IAAIpB,eAAemB,aAAnB,CAAD;AAENX,aAAOa,SAAPb,OAAAA,SAAAA,OAAOa,MAAP;IACD;;AAEH,QAAMC,cAAc,MAAM;AACxBb,uBAAmB;;AAGrB,QAAMc,gBAAgB,MAAM;AAC1Bd,uBAAmB;;AAGrB,QAAMe,cAAc,MAClB,CAACC,aAAaC,UAAb,KACAlB,OAAOX,gBAAgB,YAAY,CAACC,cAAcC,SAAd;AAEvC,QAAM4B,UAAWrB,WAAe;AAC9B,QAAI,CAACI,YAAY;AACfA,mBAAa;AACbF,aAAOoB,aAAP,OAAA,SAAApB,OAAOoB,UAAYtB,KAAnB;AACAK,oBAAU,OAAV,SAAAA,WAAU;AACVC,qBAAeN,KAAD;IACf;;AAGH,QAAMc,SAAUd,WAAe;AAC7B,QAAI,CAACI,YAAY;AACfA,mBAAa;AACbF,aAAOqB,WAAP,OAAA,SAAArB,OAAOqB,QAAUvB,KAAjB;AACAK,oBAAU,OAAV,SAAAA,WAAU;AACVE,oBAAcP,KAAD;IACd;;AAGH,QAAMwB,QAAQ,MAAM;AAClB,WAAO,IAAIf,QAASgB,qBAAoB;AACtCpB,mBAAcL,WAAU;AACtB,cAAM0B,cAActB,cAAc,CAACc,YAAW;AAC9C,YAAIQ,aAAa;AACfD,0BAAgBzB,KAAD;QAChB;AACD,eAAO0B;;AAETxB,aAAOyB,WAAPzB,OAAAA,SAAAA,OAAOyB,QAAP;KARK,EASJC,KAAK,MAAM;AACZvB,mBAAawB;AACb,UAAI,CAACzB,YAAY;AACfF,eAAO4B,cAAP5B,OAAAA,SAAAA,OAAO4B,WAAP;MACD;IACF,CAdM;EAeR;AAGD,QAAMC,MAAM,MAAM;AAEhB,QAAI3B,YAAY;AACd;IACD;AAED,QAAI4B;AAGJ,QAAI;AACFA,uBAAiB9B,OAAO+B,GAAP;aACVC,OAAO;AACdF,uBAAiBvB,QAAQK,OAAOoB,KAAf;IAClB;AAEDzB,YAAQY,QAAQW,cAAhB,EACGJ,KAAKP,OADR,EAEGc,MAAOD,WAAU;AAAA,UAAA,eAAA;AAEhB,UAAI9B,YAAY;AACd;MACD;AAGD,YAAMgC,SAAQlC,gBAAAA,OAAOkC,UAAV,OAAA,gBAAmB;AAC9B,YAAMC,cAAanC,qBAAAA,OAAOmC,eAAV,OAAA,qBAAwBnD;AACxC,YAAMoD,QACJ,OAAOD,eAAe,aAClBA,WAAWlD,cAAc+C,KAAf,IACVG;AACN,YAAME,cACJH,UAAU,QACT,OAAOA,UAAU,YAAYjD,eAAeiD,SAC5C,OAAOA,UAAU,cAAcA,MAAMjD,cAAc+C,KAAf;AAEvC,UAAI/B,oBAAoB,CAACoC,aAAa;AAEpCzB,eAAOoB,KAAD;AACN;MACD;AAED/C;AAGAe,aAAOsC,UAAP,OAAA,SAAAtC,OAAOsC,OAASrD,cAAc+C,KAA9B;AAGAO,YAAMH,KAAD,EAEFV,KAAK,MAAM;AACV,YAAIV,YAAW,GAAI;AACjB,iBAAOM,MAAK;QACb;AACD;OANJ,EAQGI,KAAK,MAAM;AACV,YAAIzB,kBAAkB;AACpBW,iBAAOoB,KAAD;QACP,OAAM;AACLH,cAAG;QACJ;OAbL;KAhCJ;EAgDD;AAGD,MAAIzC,SAASY,OAAOX,WAAR,GAAsB;AAChCwC,QAAG;EACJ,OAAM;AACLP,UAAK,EAAGI,KAAKG,GAAb;EACD;AAED,SAAO;IACLvB;IACAI;IACA8B,UAAU,MAAM;AACd,YAAMC,cAActC,cAAH,OAAA,SAAGA,WAAU;AAC9B,aAAOsC,cAAcnC,UAAUC,QAAQY,QAAR;;IAEjCL;IACAC;;AAEH;;;AClNM,IAAM2B,gBAAwBC;;;ACI9B,SAASC,sBAAsB;AACpC,MAAIC,QAA0B,CAAA;AAC9B,MAAIC,eAAe;AACnB,MAAIC,WAA4BC,cAAa;AAC3CA,aAAQ;;AAEV,MAAIC,gBAAsCD,cAAyB;AACjEA,aAAQ;;AAGV,QAAME,QAAYF,cAAyB;AACzC,QAAIG;AACJL;AACA,QAAI;AACFK,eAASH,SAAQ;IAClB,UAFD;AAGEF;AACA,UAAI,CAACA,cAAc;AACjBM,cAAK;MACN;IACF;AACD,WAAOD;;AAGT,QAAME,WAAYL,cAAmC;AACnD,QAAIF,cAAc;AAChBD,YAAMS,KAAKN,QAAX;IACD,OAAM;AACLO,wBAAkB,MAAM;AACtBR,iBAASC,QAAD;MACT,CAFgB;IAGlB;;AAMH,QAAMQ,aACJR,cAC0B;AAC1B,WAAO,IAAIS,SAAS;AAClBJ,eAAS,MAAM;AACbL,iBAAS,GAAGS,IAAJ;MACT,CAFO;;;AAMZ,QAAML,QAAQ,MAAY;AACxB,UAAMM,gBAAgBb;AACtBA,YAAQ,CAAA;AACR,QAAIa,cAAcC,QAAQ;AACxBJ,wBAAkB,MAAM;AACtBN,sBAAc,MAAM;AAClBS,wBAAcE,QAASZ,cAAa;AAClCD,qBAASC,QAAD;WADV;QAGD,CAJY;MAKd,CANgB;IAOlB;;AAOH,QAAMa,oBAAqBC,QAAuB;AAChDf,eAAWe;;AAOb,QAAMC,yBAA0BD,QAA4B;AAC1Db,oBAAgBa;;AAGlB,SAAO;IACLZ;IACAM;IACAH;IACAQ;IACAE;;AAEH;AAGYC,IAAAA,gBAAgBpB,oBAAmB;;;ACjGzC,IAAeqB,YAAf,MAAyB;EAI9BC,UAAgB;AACd,SAAKC,eAAL;EACD;EAESC,aAAmB;AAC3B,SAAKD,eAAL;AAEA,QAAIE,eAAe,KAAKC,SAAN,GAAkB;AAClC,WAAKC,YAAYC,WAAW,MAAM;AAChC,aAAKC,eAAL;SACC,KAAKH,SAFmB;IAG5B;EACF;EAESI,gBAAgBC,cAAwC;AAEhE,SAAKL,YAAYM,KAAKC,IACpB,KAAKP,aAAa,GAClBK,gBAAAA,OAAAA,eAAiBG,WAAWC,WAAW,IAAI,KAAK,GAFjC;EAIlB;EAESZ,iBAAiB;AACzB,QAAI,KAAKI,WAAW;AAClBS,mBAAa,KAAKT,SAAN;AACZ,WAAKA,YAAYU;IAClB;EACF;AA/B6B;;;AC0IzB,IAAMC,QAAN,cAKGC,UAAU;EAiBlBC,YAAYC,QAA6D;AACvE,UAAA;AAEA,SAAKC,sBAAsB;AAC3B,SAAKC,iBAAiBF,OAAOE;AAC7B,SAAKC,WAAWH,OAAOI,OAAvB;AACA,SAAKC,YAAY,CAAA;AACjB,SAAKC,QAAQN,OAAOM;AACpB,SAAKC,SAASP,OAAOO,UAAUC;AAC/B,SAAKC,WAAWT,OAAOS;AACvB,SAAKC,YAAYV,OAAOU;AACxB,SAAKC,eAAeX,OAAOY,SAASC,gBAAgB,KAAKT,OAAN;AACnD,SAAKQ,QAAQ,KAAKD;AAClB,SAAKG,WAAL;EACD;EAEO,IAAJC,OAA8B;AAChC,WAAO,KAAKX,QAAQW;EACrB;EAEOZ,WACNC,SACM;AACN,SAAKA,UAAU,kCAAK,KAAKF,iBAAmBE;AAE5C,SAAKY,gBAAgB,KAAKZ,QAAQa,SAAlC;EACD;EAESC,iBAAiB;AACzB,QAAI,CAAC,KAAKb,UAAUc,UAAU,KAAKP,MAAMQ,gBAAgB,QAAQ;AAC/D,WAAKd,MAAMe,OAAO,IAAlB;IACD;EACF;EAEDC,QACEC,SACAnB,SACO;AACP,UAAMoB,OAAOC,YAAY,KAAKb,MAAMY,MAAMD,SAAS,KAAKnB,OAAhC;AAGxB,SAAKsB,SAAS;MACZF;MACAG,MAAM;MACNC,eAAexB,WAAAA,OAAAA,SAAAA,QAASyB;MACxBC,QAAQ1B,WAAAA,OAAAA,SAAAA,QAAS0B;KAJnB;AAOA,WAAON;EACR;EAEDO,SACEnB,OACAoB,iBACM;AACN,SAAKN,SAAS;MAAEC,MAAM;MAAYf;MAAOoB;KAAzC;EACD;EAEDC,OAAO7B,SAAwC;AAAA,QAAA;AAC7C,UAAM8B,UAAU,KAAKA;AACrB,KAAA,gBAAA,KAAKC,YAAL,OAAA,SAAA,cAAcF,OAAO7B,OAArB;AACA,WAAO8B,UAAUA,QAAQE,KAAKC,IAAb,EAAmBC,MAAMD,IAAzB,IAAiCE,QAAQC,QAAR;EACnD;EAEDC,UAAgB;AACd,UAAMA,QAAN;AAEA,SAAKR,OAAO;MAAES,QAAQ;KAAtB;EACD;EAEDC,QAAc;AACZ,SAAKF,QAAL;AACA,SAAKV,SAAS,KAAKpB,YAAnB;EACD;EAEDiC,WAAoB;AAClB,WAAO,KAAKvC,UAAUwC,KAAMC,cAAaA,SAAS1C,QAAQ2C,YAAY,KAA/D;EACR;EAEDC,aAAsB;AACpB,WAAO,KAAKC,kBAAL,IAA2B,KAAK,CAAC,KAAKL,SAAL;EACzC;EAEDM,UAAmB;AACjB,WACE,KAAKtC,MAAMuC,iBACX,CAAC,KAAKvC,MAAMgB,iBACZ,KAAKvB,UAAUwC,KAAMC,cAAaA,SAASM,iBAAT,EAA4BF,OAA9D;EAEH;EAEDG,cAAcC,YAAY,GAAY;AACpC,WACE,KAAK1C,MAAMuC,iBACX,CAAC,KAAKvC,MAAMgB,iBACZ,CAAC2B,eAAe,KAAK3C,MAAMgB,eAAe0B,SAA3B;EAElB;EAEDE,UAAgB;AAAA,QAAA;AACd,UAAMV,WAAW,KAAKzC,UAAUoD,KAAMC,OAAMA,EAAEC,yBAAF,CAA3B;AAEjB,QAAIb,UAAU;AACZA,eAASc,QAAQ;QAAEC,eAAe;OAAlC;IACD;AAGD,KAAK1B,iBAAAA,KAAAA,YAAL,OAAA,SAAA,eAAc2B,SAAd;EACD;EAEDC,WAAiB;AAAA,QAAA;AACf,UAAMjB,WAAW,KAAKzC,UAAUoD,KAAMC,OAAMA,EAAEM,uBAAF,CAA3B;AAEjB,QAAIlB,UAAU;AACZA,eAASc,QAAQ;QAAEC,eAAe;OAAlC;IACD;AAGD,KAAK1B,iBAAAA,KAAAA,YAAL,OAAA,SAAA,eAAc2B,SAAd;EACD;EAEDG,YAAYnB,UAAwD;AAClE,QAAI,CAAC,KAAKzC,UAAU6D,SAASpB,QAAxB,GAAmC;AACtC,WAAKzC,UAAU8D,KAAKrB,QAApB;AAGA,WAAKsB,eAAL;AAEA,WAAK9D,MAAM+D,OAAO;QAAE1C,MAAM;QAAiB2C,OAAO;QAAMxB;OAAxD;IACD;EACF;EAEDyB,eAAezB,UAAwD;AACrE,QAAI,KAAKzC,UAAU6D,SAASpB,QAAxB,GAAmC;AACrC,WAAKzC,YAAY,KAAKA,UAAUmE,OAAQd,OAAMA,MAAMZ,QAAnC;AAEjB,UAAI,CAAC,KAAKzC,UAAUc,QAAQ;AAG1B,YAAI,KAAKgB,SAAS;AAChB,cAAI,KAAKlC,qBAAqB;AAC5B,iBAAKkC,QAAQF,OAAO;cAAEwC,QAAQ;aAA9B;UACD,OAAM;AACL,iBAAKtC,QAAQuC,YAAb;UACD;QACF;AAED,aAAK5D,WAAL;MACD;AAED,WAAKR,MAAM+D,OAAO;QAAE1C,MAAM;QAAmB2C,OAAO;QAAMxB;OAA1D;IACD;EACF;EAEDG,oBAA4B;AAC1B,WAAO,KAAK5C,UAAUc;EACvB;EAEDwD,aAAmB;AACjB,QAAI,CAAC,KAAK/D,MAAMuC,eAAe;AAC7B,WAAKzB,SAAS;QAAEC,MAAM;OAAtB;IACD;EACF;EAEDiD,MACExE,SACAyE,cACgB;AAAA,QAAA,uBAAA;AAChB,QAAI,KAAKjE,MAAMQ,gBAAgB,QAAQ;AACrC,UAAI,KAAKR,MAAMgB,iBAAiBiD,gBAAAA,QAAAA,aAAchB,eAAe;AAE3D,aAAK5B,OAAO;UAAES,QAAQ;SAAtB;MACD,WAAU,KAAKR,SAAS;AAAA,YAAA;AAEvB,SAAA,iBAAA,KAAKC,YAAL,OAAA,SAAA,eAAc2C,cAAd;AAEA,eAAO,KAAK5C;MACb;IACF;AAGD,QAAI9B,SAAS;AACX,WAAKD,WAAWC,OAAhB;IACD;AAID,QAAI,CAAC,KAAKA,QAAQ2E,SAAS;AACzB,YAAMjC,WAAW,KAAKzC,UAAUoD,KAAMC,OAAMA,EAAEtD,QAAQ2E,OAArC;AACjB,UAAIjC,UAAU;AACZ,aAAK3C,WAAW2C,SAAS1C,OAAzB;MACD;IACF;AAED,QAAI4E,MAAuC;AACzC,UAAI,CAACC,MAAMC,QAAQ,KAAK9E,QAAQK,QAA3B,GAAsC;AACzC,aAAKF,OAAO4E,MAAZ,qIAAA;MAGD;IACF;AAED,UAAMC,kBAAkBC,mBAAkB;AAG1C,UAAMC,iBAAkD;MACtD7E,UAAU,KAAKA;MACf8E,WAAWC;MACXzE,MAAM,KAAKA;IAH2C;AASxD,UAAM0E,oBAAqBC,YAAoB;AAC7CC,aAAOC,eAAeF,QAAQ,UAAU;QACtCG,YAAY;QACZC,KAAK,MAAM;AACT,cAAIV,iBAAiB;AACnB,iBAAKnF,sBAAsB;AAC3B,mBAAOmF,gBAAgBW;UACxB;AACD,iBAAOP;QACR;OARH;;AAYFC,sBAAkBH,cAAD;AAGjB,UAAMU,UAAU,MAAM;AACpB,UAAI,CAAC,KAAK5F,QAAQ2E,SAAS;AACzB,eAAOxC,QAAQ0D,OAAR,mCAC4B,KAAK7F,QAAQM,YADhD,GAAA;MAGD;AACD,WAAKT,sBAAsB;AAC3B,aAAO,KAAKG,QAAQ2E,QAAQO,cAArB;IACR;AAGD,UAAMY,UAAgE;MACpErB;MACAzE,SAAS,KAAKA;MACdK,UAAU,KAAKA;MACfG,OAAO,KAAKA;MACZoF;;AAGFP,sBAAkBS,OAAD;AAEjB,KAAK9F,wBAAAA,KAAAA,QAAQ+F,aAAb,OAAA,SAAA,sBAAuBC,QAAQF,OAA/B;AAGA,SAAKG,cAAc,KAAKzF;AAGxB,QACE,KAAKA,MAAMQ,gBAAgB,UAC3B,KAAKR,MAAM0F,gBAAX,wBAAyBJ,QAAQrB,iBAAjC,OAAA,SAAyB,sBAAsB9D,OAC/C;AAAA,UAAA;AACA,WAAKW,SAAS;QAAEC,MAAM;QAASZ,OAAMmF,yBAAAA,QAAQrB,iBAAV,OAAA,SAAE,uBAAsB9D;OAA3D;IACD;AAED,UAAMwF,UAAWpB,WAAyC;AAExD,UAAI,EAAEqB,iBAAiBrB,KAAD,KAAWA,MAAMzC,SAAS;AAC9C,aAAKhB,SAAS;UACZC,MAAM;UACNwD;SAFF;MAID;AAED,UAAI,CAACqB,iBAAiBrB,KAAD,GAAS;AAAA,YAAA,uBAAA,oBAAA,wBAAA;AAE5B,SAAK7E,yBAAAA,qBAAAA,KAAAA,MAAMN,QAAOuG,YAAUpB,OAAAA,SAAAA,sBAAAA,KAAAA,oBAAAA,OAAO,IAAnC;AACA,SAAA,0BAAA,sBAAA,KAAK7E,MAAMN,QAAOyG,cAAlB,OAAA,SAAA,uBAAA,KAAA,qBACE,KAAK7F,MAAMY,MACX2D,OACA,IAHF;AAMA,YAAIH,MAAuC;AACzC,eAAKzE,OAAO4E,MAAMA,KAAlB;QACD;MACF;AAED,UAAI,CAAC,KAAKuB,sBAAsB;AAE9B,aAAK5F,WAAL;MACD;AACD,WAAK4F,uBAAuB;IAC7B;AAGD,SAAKvE,UAAUwE,cAAc;MAC3BC,IAAIV,QAAQF;MACZa,OAAOzB,mBAAF,OAAA,SAAEA,gBAAiByB,MAAMC,KAAK1B,eAA5B;MACP2B,WAAYvF,UAAS;AAAA,YAAA,wBAAA,qBAAA,wBAAA;AACnB,YAAI,OAAOA,SAAS,aAAa;AAC/B,cAAIwD,MAAuC;AACzC,iBAAKzE,OAAO4E,MAAZ,2IAC2I,KAAKzE,SADhJ;UAGD;AACD6F,kBAAQ,IAAIS,MAAS,KAAKtG,YAAlB,oBAAA,CAAD;AACP;QACD;AAED,aAAKY,QAAQE,IAAb;AAGA,SAAKlB,0BAAAA,sBAAAA,KAAAA,MAAMN,QAAO+G,cAAYvF,OAAAA,SAAAA,uBAAAA,KAAAA,qBAAAA,MAAM,IAApC;AACA,SAAA,0BAAA,sBAAA,KAAKlB,MAAMN,QAAOyG,cAAlB,OAAA,SAAA,uBAAA,KAAA,qBACEjF,MACA,KAAKZ,MAAMuE,OACX,IAHF;AAMA,YAAI,CAAC,KAAKuB,sBAAsB;AAE9B,eAAK5F,WAAL;QACD;AACD,aAAK4F,uBAAuB;;MAE9BH;MACAU,QAAQ,CAACC,cAAc/B,UAAU;AAC/B,aAAKzD,SAAS;UAAEC,MAAM;UAAUuF;UAAc/B;SAA9C;;MAEFgC,SAAS,MAAM;AACb,aAAKzF,SAAS;UAAEC,MAAM;SAAtB;;MAEFyF,YAAY,MAAM;AAChB,aAAK1F,SAAS;UAAEC,MAAM;SAAtB;;MAEF0F,OAAOnB,QAAQ9F,QAAQiH;MACvBC,YAAYpB,QAAQ9F,QAAQkH;MAC5BC,aAAarB,QAAQ9F,QAAQmH;IA1CF,CAAD;AA6C5B,SAAKrF,UAAU,KAAKC,QAAQD;AAE5B,WAAO,KAAKA;EACb;EAEOR,SAAS8F,QAAqC;AACpD,UAAMC,UACJ7G,WAC8B;AAAA,UAAA,cAAA;AAC9B,cAAQ4G,OAAO7F,MAAf;QACE,KAAK;AACH,iBAAO,iCACFf,QADE;YAEL8G,mBAAmBF,OAAON;YAC1BS,oBAAoBH,OAAOrC;;QAE/B,KAAK;AACH,iBAAO,iCACFvE,QADE;YAELQ,aAAa;;QAEjB,KAAK;AACH,iBAAO,iCACFR,QADE;YAELQ,aAAa;;QAEjB,KAAK;AACH,iBAAO,gDACFR,QADE;YAEL8G,mBAAmB;YACnBC,oBAAoB;YACpBrB,YAAWkB,eAAAA,OAAOzG,SAAT,OAAA,eAAiB;YAC1BK,aAAawG,SAAS,KAAKxH,QAAQmH,WAAd,IACjB,aACA;cACA,CAAC3G,MAAMgB,iBAAiB;YAC1BuD,OAAO;YACP0C,QAAQ;;QAGd,KAAK;AACH,iBAAO,gDACFjH,QADE;YAELY,MAAMgG,OAAOhG;YACbsG,iBAAiBlH,MAAMkH,kBAAkB;YACzClG,gBAAa,wBAAE4F,OAAO5F,kBAAT,OAAA,wBAA0BmG,KAAKC,IAAL;YACvC7C,OAAO;YACPhC,eAAe;YACf0E,QAAQ;cACJ,CAACL,OAAO1F,UAAU;YACpBV,aAAa;YACbsG,mBAAmB;YACnBC,oBAAoB;;QAG1B,KAAK;AACH,gBAAMxC,QAAQqC,OAAOrC;AAErB,cAAIqB,iBAAiBrB,KAAD,KAAWA,MAAMV,UAAU,KAAK4B,aAAa;AAC/D,mBAAO,iCAAK,KAAKA,cAAV;cAAuBjF,aAAa;;UAC5C;AAED,iBAAO,iCACFR,QADE;YAELuE;YACA8C,kBAAkBrH,MAAMqH,mBAAmB;YAC3CC,gBAAgBH,KAAKC,IAAL;YAChBN,mBAAmB9G,MAAM8G,oBAAoB;YAC7CC,oBAAoBxC;YACpB/D,aAAa;YACbyG,QAAQ;;QAEZ,KAAK;AACH,iBAAO,iCACFjH,QADE;YAELuC,eAAe;;QAEnB,KAAK;AACH,iBAAO,kCACFvC,QACA4G,OAAO5G;MAvEhB;;AA4EF,SAAKA,QAAQ6G,QAAQ,KAAK7G,KAAN;AAEpBuH,kBAAcC,MAAM,MAAM;AACxB,WAAK/H,UAAUgI,QAASvF,cAAa;AACnCA,iBAASwF,cAAcd,MAAvB;OADF;AAIA,WAAKlH,MAAM+D,OAAO;QAAEC,OAAO;QAAM3C,MAAM;QAAW6F;OAAlD;KALF;EAOD;AAnciB;AAscpB,SAAS3G,gBAMPT,SAC2B;AAC3B,QAAMoB,OACJ,OAAOpB,QAAQmI,gBAAgB,aAC1BnI,QAAQmI,YAAT,IACAnI,QAAQmI;AAEd,QAAMC,UAAU,OAAOhH,SAAS;AAEhC,QAAMiH,uBAAuBD,UACzB,OAAOpI,QAAQqI,yBAAyB,aACrCrI,QAAQqI,qBAAT,IACArI,QAAQqI,uBACV;AAEJ,SAAO;IACLjH;IACAsG,iBAAiB;IACjBlG,eAAe4G,UAAUC,wBAAAA,OAAAA,uBAAwBV,KAAKC,IAAL,IAAa;IAC9D7C,OAAO;IACP8C,kBAAkB;IAClBC,gBAAgB;IAChBR,mBAAmB;IACnBC,oBAAoB;IACpBrB,WAAW;IACXnD,eAAe;IACf0E,QAAQW,UAAU,YAAY;IAC9BpH,aAAa;;AAEhB;;;AC5iBM,IAAMsH,aAAN,cAAyBC,aAAiC;EAM/DC,YAAYC,QAA2B;AACrC,UAAA;AACA,SAAKA,SAASA,UAAU,CAAA;AACxB,SAAKC,UAAU,CAAA;AACf,SAAKC,aAAa,CAAA;EACnB;EAEDC,MACEC,QACAC,SACAC,OAC+C;AAAA,QAAA;AAC/C,UAAMC,WAAWF,QAAQE;AACzB,UAAMC,aACJH,qBAAAA,QAAQG,cAAaC,OAAAA,qBAAAA,sBAAsBF,UAAUF,OAAX;AAC5C,QAAIK,QAAQ,KAAKC,IAA4CH,SAAjD;AAEZ,QAAI,CAACE,OAAO;AACVA,cAAQ,IAAIE,MAAM;QAChBC,OAAO;QACPC,QAAQV,OAAOW,UAAP;QACRR;QACAC;QACAH,SAASD,OAAOY,oBAAoBX,OAA3B;QACTC;QACAW,gBAAgBb,OAAOc,iBAAiBX,QAAxB;MAPA,CAAV;AASR,WAAKY,IAAIT,KAAT;IACD;AAED,WAAOA;EACR;EAEDS,IAAIT,OAAwC;AAC1C,QAAI,CAAC,KAAKR,WAAWQ,MAAMF,SAAtB,GAAkC;AACrC,WAAKN,WAAWQ,MAAMF,SAAtB,IAAmCE;AACnC,WAAKT,QAAQmB,KAAKV,KAAlB;AACA,WAAKW,OAAO;QACVC,MAAM;QACNZ;OAFF;IAID;EACF;EAEDa,OAAOb,OAAwC;AAC7C,UAAMc,aAAa,KAAKtB,WAAWQ,MAAMF,SAAtB;AAEnB,QAAIgB,YAAY;AACdd,YAAMe,QAAN;AAEA,WAAKxB,UAAU,KAAKA,QAAQyB,OAAQC,OAAMA,MAAMjB,KAAjC;AAEf,UAAIc,eAAed,OAAO;AACxB,eAAO,KAAKR,WAAWQ,MAAMF,SAAtB;MACR;AAED,WAAKa,OAAO;QAAEC,MAAM;QAAWZ;OAA/B;IACD;EACF;EAEDkB,QAAc;AACZC,kBAAcC,MAAM,MAAM;AACxB,WAAK7B,QAAQ8B,QAASrB,WAAU;AAC9B,aAAKa,OAAOb,KAAZ;OADF;KADF;EAKD;EAEDC,IAMEH,WAC2D;AAC3D,WAAO,KAAKN,WAAWM,SAAhB;EACR;EAEDwB,SAAkB;AAChB,WAAO,KAAK/B;EACb;;;;EAeDgC,KACEC,MACAC,MACgD;AAChD,UAAM,CAACC,OAAD,IAAYC,gBAAgBH,MAAMC,IAAP;AAEjC,QAAI,OAAOC,QAAQE,UAAU,aAAa;AACxCF,cAAQE,QAAQ;IACjB;AAED,WAAO,KAAKrC,QAAQgC,KAAMvB,WAAU6B,WAAWH,SAAS1B,KAAV,CAAvC;EACR;;;;EAoBD8B,QACEN,MACAC,MACS;AACT,UAAM,CAACC,OAAD,IAAYC,gBAAgBH,MAAMC,IAAP;AACjC,WAAOM,OAAOC,KAAKN,OAAZ,EAAqBO,SAAS,IACjC,KAAK1C,QAAQyB,OAAQhB,WAAU6B,WAAWH,SAAS1B,KAAV,CAAzC,IACA,KAAKT;EACV;EAEDoB,OAAOuB,OAA8B;AACnCf,kBAAcC,MAAM,MAAM;AACxB,WAAKe,UAAUd,QAAQ,CAAC;QAAEe;MAAF,MAAiB;AACvCA,iBAASF,KAAD;OADV;KADF;EAKD;EAEDG,UAAgB;AACdlB,kBAAcC,MAAM,MAAM;AACxB,WAAK7B,QAAQ8B,QAASrB,WAAU;AAC9BA,cAAMqC,QAAN;OADF;KADF;EAKD;EAEDC,WAAiB;AACfnB,kBAAcC,MAAM,MAAM;AACxB,WAAK7B,QAAQ8B,QAASrB,WAAU;AAC9BA,cAAMsC,SAAN;OADF;KADF;EAKD;AArK8D;;;ACM1D,IAAMC,WAAN,cAKGC,UAAU;EAWlBC,YAAYC,QAA6D;AACvE,UAAA;AAEA,SAAKC,iBAAiBD,OAAOC;AAC7B,SAAKC,aAAaF,OAAOE;AACzB,SAAKC,gBAAgBH,OAAOG;AAC5B,SAAKC,SAASJ,OAAOI,UAAUC;AAC/B,SAAKC,YAAY,CAAA;AACjB,SAAKC,QAAQP,OAAOO,SAASC,iBAAe;AAE5C,SAAKC,WAAWT,OAAOU,OAAvB;AACA,SAAKC,WAAL;EACD;EAEDF,WACEC,SACM;AACN,SAAKA,UAAU,kCAAK,KAAKT,iBAAmBS;AAE5C,SAAKE,gBAAgB,KAAKF,QAAQG,SAAlC;EACD;EAEO,IAAJC,OAAiC;AACnC,WAAO,KAAKJ,QAAQI;EACrB;EAEDC,SAASR,OAAiE;AACxE,SAAKS,SAAS;MAAEC,MAAM;MAAYV;KAAlC;EACD;EAEDW,YAAYC,UAAsD;AAChE,QAAI,CAAC,KAAKb,UAAUc,SAASD,QAAxB,GAAmC;AACtC,WAAKb,UAAUe,KAAKF,QAApB;AAGA,WAAKG,eAAL;AAEA,WAAKnB,cAAcoB,OAAO;QACxBN,MAAM;QACNO,UAAU;QACVL;OAHF;IAKD;EACF;EAEDM,eAAeN,UAAsD;AACnE,SAAKb,YAAY,KAAKA,UAAUoB,OAAQC,OAAMA,MAAMR,QAAnC;AAEjB,SAAKR,WAAL;AAEA,SAAKR,cAAcoB,OAAO;MACxBN,MAAM;MACNO,UAAU;MACVL;KAHF;EAKD;EAESS,iBAAiB;AACzB,QAAI,CAAC,KAAKtB,UAAUuB,QAAQ;AAC1B,UAAI,KAAKtB,MAAMuB,WAAW,WAAW;AACnC,aAAKnB,WAAL;MACD,OAAM;AACL,aAAKR,cAAc4B,OAAO,IAA1B;MACD;IACF;EACF;EAEDC,WAA6B;AAAA,QAAA,uBAAA;AAC3B,YAAO,yBAAA,gBAAA,KAAKC,YAAL,OAAA,SAAA,cAAcD,SAAd,MAAP,OAAA,wBAAmC,KAAKE,QAAL;EACpC;EAEKA,UAA0B;;AAC9B,YAAMC,kBAAkB,MAAM;AAAA,YAAA;AAC5B,aAAKF,UAAUG,cAAc;UAC3BC,IAAI,MAAM;AACR,gBAAI,CAAC,KAAK3B,QAAQ4B,YAAY;AAC5B,qBAAOC,QAAQC,OAAO,qBAAf;YACR;AACD,mBAAO,KAAK9B,QAAQ4B,WAAW,KAAK/B,MAAMkC,SAAnC;;UAETC,QAAQ,CAACC,cAAcC,UAAU;AAC/B,iBAAK5B,SAAS;cAAEC,MAAM;cAAU0B;cAAcC;aAA9C;;UAEFC,SAAS,MAAM;AACb,iBAAK7B,SAAS;cAAEC,MAAM;aAAtB;;UAEF6B,YAAY,MAAM;AAChB,iBAAK9B,SAAS;cAAEC,MAAM;aAAtB;;UAEF8B,QAAK,sBAAE,KAAKrC,QAAQqC,UAAf,OAAA,sBAAwB;UAC7BC,YAAY,KAAKtC,QAAQsC;UACzBC,aAAa,KAAKvC,QAAQuC;QAlBC,CAAD;AAqB5B,eAAO,KAAKhB,QAAQiB;;AAGtB,YAAMC,WAAW,KAAK5C,MAAMuB,WAAW;AACvC,UAAI;AAAA,YAAA,wBAAA,wBAAA,uBAAA,gBAAA,wBAAA,wBAAA,uBAAA;AACF,YAAI,CAACqB,UAAU;AAAA,cAAA,uBAAA,wBAAA,uBAAA;AACb,eAAKnC,SAAS;YAAEC,MAAM;YAAWwB,WAAW,KAAK/B,QAAQ+B;UAA3C,CAAd;AAEA,iBAAA,yBAAWtC,yBAAAA,KAAAA,cAAcH,QAAOoD,aAAhC,OAAA,SAAM,sBACJ,KAAA,wBAAA,KAAK7C,MAAMkC,WACX,IAFI;AAIN,gBAAMY,UAAU,OAAM,yBAAA,gBAAA,KAAK3C,SAAQ0C,aAAb,OAAA,SAAA,sBAAA,KAAA,eAAwB,KAAK7C,MAAMkC,SAAnC;AACtB,cAAIY,YAAY,KAAK9C,MAAM8C,SAAS;AAClC,iBAAKrC,SAAS;cACZC,MAAM;cACNoC;cACAZ,WAAW,KAAKlC,MAAMkC;aAHxB;UAKD;QACF;AACD,cAAMa,OAAO,MAAMnB,gBAAe;AAGlC,eAAM,0BAAA,yBAAA,KAAKhC,cAAcH,QAAOuD,cAAhC,OAAA,SAAM,uBAAA,KAAA,wBACJD,MACA,KAAK/C,MAAMkC,WACX,KAAKlC,MAAM8C,SACX,IAJI;AAON,eAAA,yBAAM,iBAAA,KAAK3C,SAAQ6C,cAAb,OAAA,SAAA,sBAAA,KAAA,gBACJD,MACA,KAAK/C,MAAMkC,WACX,KAAKlC,MAAM8C,OAHP;AAON,eAAM,0BAAA,yBAAA,KAAKlD,cAAcH,QAAOwD,cAAhC,OAAA,SAAM,uBACJF,KAAAA,wBAAAA,MACA,MACA,KAAK/C,MAAMkC,WACX,KAAKlC,MAAM8C,SACX,IALI;AAQN,eAAA,yBAAM,iBAAA,KAAK3C,SAAQ8C,cAAb,OAAA,SAAA,sBAAA,KAAA,gBACJF,MACA,MACA,KAAK/C,MAAMkC,WACX,KAAKlC,MAAM8C,OAJP;AAON,aAAKrC,SAAS;UAAEC,MAAM;UAAWqC;SAAjC;AACA,eAAOA;eACAV,OAAO;AACd,YAAI;AAAA,cAAA,wBAAA,wBAAA,uBAAA,gBAAA,wBAAA,yBAAA,wBAAA;AAEF,iBAAM,0BAAA,yBAAA,KAAKzC,cAAcH,QAAOyD,YAAhC,OAAA,SAAM,uBAAA,KAAA,wBACJb,OACA,KAAKrC,MAAMkC,WACX,KAAKlC,MAAM8C,SACX,IAJI;AAON,cAAIK,MAAuC;AACzC,iBAAKtD,OAAOwC,MAAMA,KAAlB;UACD;AAED,iBAAA,yBAAM,iBAAA,KAAKlC,SAAQ+C,YAAb,OAAA,SAAA,sBAAA,KAAA,gBACJb,OACA,KAAKrC,MAAMkC,WACX,KAAKlC,MAAM8C,OAHP;AAON,iBAAM,0BAAA,0BAAA,KAAKlD,cAAcH,QAAOwD,cAAhC,OAAA,SAAM,uBACJG,KAAAA,yBAAAA,QACAf,OACA,KAAKrC,MAAMkC,WACX,KAAKlC,MAAM8C,SACX,IALI;AAQN,iBAAA,0BAAM,iBAAA,KAAK3C,SAAQ8C,cAAb,OAAA,SAAA,uBAAA,KAAA,gBACJG,QACAf,OACA,KAAKrC,MAAMkC,WACX,KAAKlC,MAAM8C,OAJP;AAMN,gBAAMT;QACP,UAnCD;AAoCE,eAAK5B,SAAS;YAAEC,MAAM;YAAS2B;WAA/B;QACD;MACF;IACF;;EAEO5B,SAAS4C,QAA2D;AAC1E,UAAMC,UACJtD,WACuD;AACvD,cAAQqD,OAAO3C,MAAf;QACE,KAAK;AACH,iBAAO,iCACFV,QADE;YAELoC,cAAciB,OAAOjB;YACrBmB,eAAeF,OAAOhB;;QAE1B,KAAK;AACH,iBAAO,iCACFrC,QADE;YAELwD,UAAU;;QAEd,KAAK;AACH,iBAAO,iCACFxD,QADE;YAELwD,UAAU;;QAEd,KAAK;AACH,iBAAO,iCACFxD,QADE;YAEL8C,SAASO,OAAOP;YAChBC,MAAMK;YACNhB,cAAc;YACdmB,eAAe;YACflB,OAAO;YACPmB,UAAU,CAACC,SAAS,KAAKtD,QAAQuC,WAAd;YACnBnB,QAAQ;YACRW,WAAWmB,OAAOnB;;QAEtB,KAAK;AACH,iBAAO,iCACFlC,QADE;YAEL+C,MAAMM,OAAON;YACbX,cAAc;YACdmB,eAAe;YACflB,OAAO;YACPd,QAAQ;YACRiC,UAAU;;QAEd,KAAK;AACH,iBAAO,iCACFxD,QADE;YAEL+C,MAAMK;YACNf,OAAOgB,OAAOhB;YACdD,cAAcpC,MAAMoC,eAAe;YACnCmB,eAAeF,OAAOhB;YACtBmB,UAAU;YACVjC,QAAQ;;QAEZ,KAAK;AACH,iBAAO,kCACFvB,QACAqD,OAAOrD;MApDhB;;AAwDF,SAAKA,QAAQsD,QAAQ,KAAKtD,KAAN;AAEpB0D,kBAAcC,MAAM,MAAM;AACxB,WAAK5D,UAAU6D,QAAShD,cAAa;AACnCA,iBAASiD,iBAAiBR,MAA1B;OADF;AAGA,WAAKzD,cAAcoB,OAAO;QACxBC,UAAU;QACVP,MAAM;QACN2C;OAHF;KAJF;EAUD;AAlRiB;AAqRb,SAASpD,mBAKwC;AACtD,SAAO;IACL6C,SAASM;IACTL,MAAMK;IACNf,OAAO;IACPD,cAAc;IACdmB,eAAe;IACfC,UAAU;IACVjC,QAAQ;IACRW,WAAWkB;;AAEd;;;AC3SM,IAAMU,gBAAN,cAA4BC,aAAoC;EAOrEC,YAAYC,QAA8B;AACxC,UAAA;AACA,SAAKA,SAASA,UAAU,CAAA;AACxB,SAAKC,YAAY,CAAA;AACjB,SAAKC,aAAa;EACnB;EAEDC,MACEC,QACAC,SACAC,OAC+C;AAC/C,UAAMC,WAAW,IAAIC,SAAS;MAC5BC,eAAe;MACfC,QAAQN,OAAOO,UAAP;MACRT,YAAY,EAAE,KAAKA;MACnBG,SAASD,OAAOQ,uBAAuBP,OAA9B;MACTC;MACAO,gBAAgBR,QAAQS,cACpBV,OAAOW,oBAAoBV,QAAQS,WAAnC,IACAE;IARwB,CAAb;AAWjB,SAAKC,IAAIV,QAAT;AAEA,WAAOA;EACR;EAEDU,IAAIV,UAA8C;AAChD,SAAKN,UAAUiB,KAAKX,QAApB;AACA,SAAKY,OAAO;MAAEC,MAAM;MAASb;KAA7B;EACD;EAEDc,OAAOd,UAA8C;AACnD,SAAKN,YAAY,KAAKA,UAAUqB,OAAQC,OAAMA,MAAMhB,QAAnC;AACjB,SAAKY,OAAO;MAAEC,MAAM;MAAWb;KAA/B;EACD;EAEDiB,QAAc;AACZC,kBAAcC,MAAM,MAAM;AACxB,WAAKzB,UAAU0B,QAASpB,cAAa;AACnC,aAAKc,OAAOd,QAAZ;OADF;KADF;EAKD;EAEDqB,SAAqB;AACnB,WAAO,KAAK3B;EACb;EAED4B,KACEC,SAC2D;AAC3D,QAAI,OAAOA,QAAQC,UAAU,aAAa;AACxCD,cAAQC,QAAQ;IACjB;AAED,WAAO,KAAK9B,UAAU4B,KAAMtB,cAAayB,cAAcF,SAASvB,QAAV,CAA/C;EACR;EAED0B,QAAQH,SAAsC;AAC5C,WAAO,KAAK7B,UAAUqB,OAAQf,cAAayB,cAAcF,SAASvB,QAAV,CAAjD;EACR;EAEDY,OAAOe,OAAiC;AACtCT,kBAAcC,MAAM,MAAM;AACxB,WAAKS,UAAUR,QAAQ,CAAC;QAAES;MAAF,MAAiB;AACvCA,iBAASF,KAAD;OADV;KADF;EAKD;EAEDG,wBAA0C;AAAA,QAAA;AACxC,SAAKC,aAAY,iBAAA,KAAKA,aAAN,OAAA,iBAAkBC,QAAQC,QAAR,GAC/BC,KAAK,MAAM;AACV,YAAMC,kBAAkB,KAAKzC,UAAUqB,OAAQC,OAAMA,EAAEjB,MAAMqC,QAArC;AACxB,aAAOlB,cAAcC,MAAM,MACzBgB,gBAAgBE,OACd,CAACC,SAAStC,aACRsC,QAAQJ,KAAK,MAAMlC,SAASuC,SAAT,EAAoBC,MAAMC,IAA1B,CAAnB,GACFT,QAAQC,QAAR,CAHF,CADK;KAHK,EAWbC,KAAK,MAAM;AACV,WAAKH,WAAWtB;IACjB,CAba;AAehB,WAAO,KAAKsB;EACb;AAhGoE;;;AC1EhE,SAASW,wBAI8C;AAC5D,SAAO;IACLC,SAAUC,aAAY;AACpBA,cAAQC,UAAU,MAAM;AAAA,YAAA,uBAAA,wBAAA,wBAAA,wBAAA,qBAAA;AACtB,cAAMC,eACJF,wBAAAA,QAAQG,iBADuD,OAAA,UAAA,yBAC/D,sBAAsBC,SAAtB,OAAA,SAAA,uBAA4BF;AAC9B,cAAMG,aAAYL,yBAAAA,QAAQG,iBAAX,OAAA,UAAA,yBAAG,uBAAsBC,SAAtB,OAAA,SAAA,uBAA4BC;AAC9C,cAAMC,YAAYD,aAAAA,OAAAA,SAAAA,UAAWC;AAC7B,cAAMC,sBAAqBF,aAAS,OAAT,SAAAA,UAAWG,eAAc;AACpD,cAAMC,0BAAyBJ,aAAS,OAAT,SAAAA,UAAWG,eAAc;AACxD,cAAME,aAAW,sBAAAV,QAAQW,MAAMC,SAAd,OAAA,SAAA,oBAAoBC,UAAS,CAAA;AAC9C,cAAMC,kBAAgB,uBAAAd,QAAQW,MAAMC,SAAd,OAAA,SAAA,qBAAoBG,eAAc,CAAA;AACxD,YAAIC,gBAAgBF;AACpB,YAAIG,YAAY;AAEhB,cAAMC,oBAAqBC,YAAoB;AAC7CC,iBAAOC,eAAeF,QAAQ,UAAU;YACtCG,YAAY;YACZC,KAAK,MAAM;AAAA,kBAAA;AACT,mBAAA,kBAAIvB,QAAQwB,WAAR,QAAA,gBAAgBC,SAAS;AAC3BR,4BAAY;cACb,OAAM;AAAA,oBAAA;AACL,iBAAAjB,mBAAAA,QAAQwB,WAAR,OAAA,SAAA,iBAAgBE,iBAAiB,SAAS,MAAM;AAC9CT,8BAAY;iBADd;cAGD;AACD,qBAAOjB,QAAQwB;YAChB;WAXH;QAaD;AAGD,cAAMG,UACJ3B,QAAQ4B,QAAQD,YACf,MACCE,QAAQC,OAAR,mCACmC9B,QAAQ4B,QAAQG,YADnD,GAAA;AAIJ,cAAMC,gBAAgB,CACpBnB,OACAoB,OACAC,MACAC,aACG;AACHnB,0BAAgBmB,WACZ,CAACF,OAAO,GAAGjB,aAAX,IACA,CAAC,GAAGA,eAAeiB,KAAnB;AACJ,iBAAOE,WAAW,CAACD,MAAM,GAAGrB,KAAV,IAAmB,CAAC,GAAGA,OAAOqB,IAAX;QACtC;AAGD,cAAME,YAAY,CAChBvB,OACAwB,QACAJ,OACAE,aACuB;AACvB,cAAIlB,WAAW;AACb,mBAAOY,QAAQC,OAAO,WAAf;UACR;AAED,cAAI,OAAOG,UAAU,eAAe,CAACI,UAAUxB,MAAMyB,QAAQ;AAC3D,mBAAOT,QAAQU,QAAQ1B,KAAhB;UACR;AAED,gBAAM2B,iBAAuC;YAC3CC,UAAUzC,QAAQyC;YAClBnC,WAAW2B;YACX7B,MAAMJ,QAAQ4B,QAAQxB;;AAGxBc,4BAAkBsB,cAAD;AAEjB,gBAAME,gBAAgBf,QAAQa,cAAD;AAE7B,gBAAMG,WAAUd,QAAQU,QAAQG,aAAhB,EAA+BE,KAAMV,UACnDF,cAAcnB,OAAOoB,OAAOC,MAAMC,QAArB,CADC;AAIhB,iBAAOQ;;AAGT,YAAIA;AAGJ,YAAI,CAACjC,SAAS4B,QAAQ;AACpBK,oBAAUP,UAAU,CAAA,CAAD;QACpB,WAGQ7B,oBAAoB;AAC3B,gBAAM8B,SAAS,OAAO/B,cAAc;AACpC,gBAAM2B,QAAQI,SACV/B,YACAuC,iBAAiB7C,QAAQ4B,SAASlB,QAAlB;AACpBiC,oBAAUP,UAAU1B,UAAU2B,QAAQJ,KAAnB;QACpB,WAGQxB,wBAAwB;AAC/B,gBAAM4B,SAAS,OAAO/B,cAAc;AACpC,gBAAM2B,QAAQI,SACV/B,YACAwC,qBAAqB9C,QAAQ4B,SAASlB,QAAlB;AACxBiC,oBAAUP,UAAU1B,UAAU2B,QAAQJ,OAAO,IAA1B;QACpB,OAGI;AACHjB,0BAAgB,CAAA;AAEhB,gBAAMqB,SAAS,OAAOrC,QAAQ4B,QAAQiB,qBAAqB;AAE3D,gBAAME,uBACJ7C,eAAeQ,SAAS,CAAD,IACnBR,YAAYQ,SAAS,CAAD,GAAK,GAAGA,QAAjB,IACX;AAGNiC,oBAAUI,uBACNX,UAAU,CAAA,GAAIC,QAAQvB,cAAc,CAAD,CAA1B,IACTe,QAAQU,QAAQP,cAAc,CAAA,GAAIlB,cAAc,CAAD,GAAKJ,SAAS,CAAD,CAA/B,CAA7B;AAGJ,mBAASsC,IAAI,GAAGA,IAAItC,SAAS4B,QAAQU,KAAK;AACxCL,sBAAUA,QAAQC,KAAM/B,WAAU;AAChC,oBAAMoC,sBACJ/C,eAAeQ,SAASsC,CAAD,IACnB9C,YAAYQ,SAASsC,CAAD,GAAKA,GAAGtC,QAAjB,IACX;AAEN,kBAAIuC,qBAAqB;AACvB,sBAAMhB,QAAQI,SACVvB,cAAckC,CAAD,IACbH,iBAAiB7C,QAAQ4B,SAASf,KAAlB;AACpB,uBAAOuB,UAAUvB,OAAOwB,QAAQJ,KAAhB;cACjB;AACD,qBAAOJ,QAAQU,QACbP,cAAcnB,OAAOC,cAAckC,CAAD,GAAKtC,SAASsC,CAAD,CAAlC,CADR;YAGR,CAfS;UAgBX;QACF;AAED,cAAME,eAAeP,QAAQC,KAAM/B,YAAW;UAC5CA;UACAE,YAAYC;QAFgC,EAAzB;AAKrB,eAAOkC;;IAEV;;AAEJ;AAEM,SAASL,iBACdjB,SACAf,OACqB;AACrB,SAAOe,QAAQiB,oBAAf,OAAA,SAAOjB,QAAQiB,iBAAmBhC,MAAMA,MAAMyB,SAAS,CAAhB,GAAoBzB,KAApD;AACR;AAEM,SAASiC,qBACdlB,SACAf,OACqB;AACrB,SAAOe,QAAQkB,wBAAf,OAAA,SAAOlB,QAAQkB,qBAAuBjC,MAAM,CAAD,GAAKA,KAAzC;AACR;AAMM,SAASsC,YACdvB,SACAf,OACqB;AACrB,MAAIe,QAAQiB,oBAAoBO,MAAMC,QAAQxC,KAAd,GAAsB;AACpD,UAAMyC,gBAAgBT,iBAAiBjB,SAASf,KAAV;AACtC,WACE,OAAOyC,kBAAkB,eACzBA,kBAAkB,QAClBA,kBAAkB;EAErB;AACD;AACD;AAMM,SAASC,gBACd3B,SACAf,OACqB;AACrB,MAAIe,QAAQkB,wBAAwBM,MAAMC,QAAQxC,KAAd,GAAsB;AACxD,UAAM2C,oBAAoBV,qBAAqBlB,SAASf,KAAV;AAC9C,WACE,OAAO2C,sBAAsB,eAC7BA,sBAAsB,QACtBA,sBAAsB;EAEzB;AACD;AACD;;;ACjKM,IAAMC,cAAN,MAAkB;EAWvBC,YAAYC,SAA4B,CAAA,GAAI;AAC1C,SAAKC,aAAaD,OAAOC,cAAc,IAAIC,WAAJ;AACvC,SAAKC,gBAAgBH,OAAOG,iBAAiB,IAAIC,cAAJ;AAC7C,SAAKC,SAASL,OAAOK,UAAUC;AAC/B,SAAKC,iBAAiBP,OAAOO,kBAAkB,CAAA;AAC/C,SAAKC,gBAAgB,CAAA;AACrB,SAAKC,mBAAmB,CAAA;AACxB,SAAKC,aAAa;AAElB,QAA6CV,OAAOK,QAAQ;AAC1D,WAAKA,OAAOM,MAAZ,4FAAA;IAGD;EACF;EAEDC,QAAc;AACZ,SAAKF;AACL,QAAI,KAAKA,eAAe,EAAG;AAE3B,SAAKG,mBAAmBC,aAAaC,UAAU,MAAM;AACnD,UAAID,aAAaE,UAAb,GAA0B;AAC5B,aAAKC,sBAAL;AACA,aAAKhB,WAAWiB,QAAhB;MACD;IACF,CALuB;AAMxB,SAAKC,oBAAoBC,cAAcL,UAAU,MAAM;AACrD,UAAIK,cAAcC,SAAd,GAA0B;AAC5B,aAAKJ,sBAAL;AACA,aAAKhB,WAAWqB,SAAhB;MACD;IACF,CALwB;EAM1B;EAEDC,UAAgB;AAAA,QAAA,uBAAA;AACd,SAAKb;AACL,QAAI,KAAKA,eAAe,EAAG;AAE3B,KAAA,wBAAA,KAAKG,qBAAL,OAAA,SAAA,sBAAA,KAAA,IAAA;AACA,SAAKA,mBAAmBW;AAExB,KAAA,wBAAA,KAAKL,sBAAL,OAAA,SAAA,sBAAA,KAAA,IAAA;AACA,SAAKA,oBAAoBK;EAC1B;;;;EAaDC,WAAWC,MAAgCC,MAA6B;AACtE,UAAM,CAACC,OAAD,IAAYC,gBAAgBH,MAAMC,IAAP;AACjCC,YAAQE,cAAc;AACtB,WAAO,KAAK7B,WAAW8B,QAAQH,OAAxB,EAAiCI;EACzC;EAEDC,WAAWL,SAAmC;AAC5C,WAAO,KAAKzB,cAAc4B,QAAQ,iCAAKH,UAAL;MAAcM,UAAU;IAAxB,EAA3B,EAA2DF;EACnE;;;;EAeDG,aACEC,UACAR,SAC0B;AAAA,QAAA;AAC1B,YAAA,wBAAO,KAAK3B,WAAWoC,KAAmBD,UAAUR,OAA7C,MAAP,OAAA,SAAO,sBAAuDU,MAAMC;EACrE;;;;EA+CDC,gBAMEd,MAMAC,MAGAc,MACgB;AAChB,UAAMC,gBAAgBC,eAAejB,MAAMC,MAAMc,IAAb;AACpC,UAAMG,aAAa,KAAKT,aAAoBO,cAAcN,QAAvC;AAEnB,WAAOQ,aACHC,QAAQC,QAAQF,UAAhB,IACA,KAAKG,WAAWL,aAAhB;EACL;;;;EAcDM,eACEC,mBACwC;AACxC,WAAO,KAAKC,cAAL,EACJnB,QAAQkB,iBADJ,EAEJE,IAAI,CAAC;MAAEf;MAAUE;IAAZ,MAAwB;AAC5B,YAAMC,OAAOD,MAAMC;AACnB,aAAO,CAACH,UAAUG,IAAX;IACR,CALI;EAMR;EAEDa,aACEhB,UACAiB,SACAC,SAC0B;AAC1B,UAAMC,QAAQ,KAAKtD,WAAWoC,KAAmBD,QAAnC;AACd,UAAMoB,WAAWD,SAAH,OAAA,SAAGA,MAAOjB,MAAMC;AAC9B,UAAMA,OAAOkB,iBAAiBJ,SAASG,QAAV;AAE7B,QAAI,OAAOjB,SAAS,aAAa;AAC/B,aAAOf;IACR;AAED,UAAMkB,gBAAgBC,eAAeP,QAAD;AACpC,UAAMsB,mBAAmB,KAAKC,oBAAoBjB,aAAzB;AACzB,WAAO,KAAKzC,WACT2D,MAAM,MAAMF,gBADR,EAEJG,QAAQtB,MAAM,iCAAKe,UAAL;MAAcQ,QAAQ;IAAtB,EAFV;EAGR;;;;EAkBDC,eACEd,mBACAI,SACAC,SACwC;AACxC,WAAOU,cAAcC,MAAM,MACzB,KAAKf,cAAL,EACGnB,QAAQkB,iBADX,EAEGE,IAAI,CAAC;MAAEf;IAAF,MAAiB,CACrBA,UACA,KAAKgB,aAA2BhB,UAAUiB,SAASC,OAAnD,CAFqB,CAFzB,CADK;EAQR;EAEDY,cACE9B,UAIAR,SAC8C;AAAA,QAAA;AAC9C,YAAO,yBAAA,KAAK3B,WAAWoC,KAA2BD,UAAUR,OAArD,MAAA,OAAA,SAAA,uBAA+DU;EACvE;;;;EAaD6B,cACEzC,MACAC,MACM;AACN,UAAM,CAACC,OAAD,IAAYC,gBAAgBH,MAAMC,IAAP;AACjC,UAAM1B,aAAa,KAAKA;AACxB+D,kBAAcC,MAAM,MAAM;AACxBhE,iBAAW8B,QAAQH,OAAnB,EAA4BwC,QAASb,WAAU;AAC7CtD,mBAAWoE,OAAOd,KAAlB;OADF;KADF;EAKD;;;;EAiBDe,aACE5C,MACAC,MACAc,MACe;AACf,UAAM,CAACb,SAAS0B,OAAV,IAAqBzB,gBAAgBH,MAAMC,MAAMc,IAAb;AAC1C,UAAMxC,aAAa,KAAKA;AAExB,UAAMsE,iBAAsC;MAC1CC,MAAM;OACH5C;AAGL,WAAOoC,cAAcC,MAAM,MAAM;AAC/BhE,iBAAW8B,QAAQH,OAAnB,EAA4BwC,QAASb,WAAU;AAC7CA,cAAMkB,MAAN;OADF;AAGA,aAAO,KAAKC,eAAeH,gBAAgBjB,OAApC;IACR,CALM;EAMR;;;;EAcDqB,cACEjD,MACAC,MACAc,MACe;AACf,UAAM,CAACb,SAASgD,gBAAgB,CAAA,CAA1B,IAAgC/C,gBAAgBH,MAAMC,MAAMc,IAAb;AAErD,QAAI,OAAOmC,cAAcC,WAAW,aAAa;AAC/CD,oBAAcC,SAAS;IACxB;AAED,UAAMC,WAAWd,cAAcC,MAAM,MACnC,KAAKhE,WACF8B,QAAQH,OADX,EAEGuB,IAAKI,WAAUA,MAAMwB,OAAOH,aAAb,CAFlB,CADe;AAMjB,WAAO/B,QAAQmC,IAAIF,QAAZ,EAAsBG,KAAKC,IAA3B,EAAiCC,MAAMD,IAAvC;EACR;;;;EAiBDE,kBACE1D,MACAC,MACAc,MACe;AACf,UAAM,CAACb,SAAS0B,OAAV,IAAqBzB,gBAAgBH,MAAMC,MAAMc,IAAb;AAE1C,WAAOuB,cAAcC,MAAM,MAAM;AAAA,UAAA,MAAA;AAC/B,WAAKhE,WAAW8B,QAAQH,OAAxB,EAAiCwC,QAASb,WAAU;AAClDA,cAAM8B,WAAN;OADF;AAIA,UAAIzD,QAAQ0D,gBAAgB,QAAQ;AAClC,eAAOzC,QAAQC,QAAR;MACR;AACD,YAAMyB,iBAAsC,iCACvC3C,UADuC;QAE1C4C,OAAI,QAAA,uBAAE5C,QAAQ0D,gBAAV,OAAA,uBAAyB1D,QAAQ4C,SAAQ,OAAA,OAAA;;AAE/C,aAAO,KAAKE,eAAeH,gBAAgBjB,OAApC;IACR,CAbM;EAcR;;;;EAiBDoB,eACEhD,MACAC,MACAc,MACe;AACf,UAAM,CAACb,SAAS0B,OAAV,IAAqBzB,gBAAgBH,MAAMC,MAAMc,IAAb;AAE1C,UAAMqC,WAAWd,cAAcC,MAAM,MACnC,KAAKhE,WACF8B,QAAQH,OADX,EAEG2D,OAAQhC,WAAU,CAACA,MAAMiC,WAAN,CAFtB,EAGGrC,IAAKI,WAAD;AAAA,UAAA;AAAA,aACHA,MAAMkC,MAAMjE,QAAW,iCAClB8B,UADkB;QAErBoC,gBAAa,wBAAEpC,WAAF,OAAA,SAAEA,QAASoC,kBAAX,OAAA,wBAA4B;QACzCC,MAAM;UAAEC,aAAahE,QAAQgE;QAAvB;MAHe,EAAvB;IADG,CAHP,CADe;AAajB,QAAIC,UAAUhD,QAAQmC,IAAIF,QAAZ,EAAsBG,KAAKC,IAA3B;AAEd,QAAI,EAAC5B,WAAD,QAACA,QAASwC,eAAc;AAC1BD,gBAAUA,QAAQV,MAAMD,IAAd;IACX;AAED,WAAOW;EACR;;;;EA4CD9C,WAMErB,MACAC,MAMAc,MAIgB;AAChB,UAAMC,gBAAgBC,eAAejB,MAAMC,MAAMc,IAAb;AACpC,UAAMiB,mBAAmB,KAAKC,oBAAoBjB,aAAzB;AAGzB,QAAI,OAAOgB,iBAAiBqC,UAAU,aAAa;AACjDrC,uBAAiBqC,QAAQ;IAC1B;AAED,UAAMxC,QAAQ,KAAKtD,WAAW2D,MAAM,MAAMF,gBAA5B;AAEd,WAAOH,MAAMyC,cAActC,iBAAiBuC,SAArC,IACH1C,MAAMkC,MAAM/B,gBAAZ,IACAb,QAAQC,QAAQS,MAAMjB,MAAMC,IAA5B;EACL;;;;EA4CD2D,cAMExE,MACAC,MAMAc,MAIe;AACf,WAAO,KAAKM,WAAWrB,MAAaC,MAAac,IAA1C,EACJwC,KAAKC,IADD,EAEJC,MAAMD,IAFF;EAGR;;;;EA4CDiB,mBAMEzE,MAGAC,MAMAc,MAI8B;AAC9B,UAAMC,gBAAgBC,eAAejB,MAAMC,MAAMc,IAAb;AACpCC,kBAAc0D,WAAWC,sBAAqB;AAK9C,WAAO,KAAKtD,WAAWL,aAAhB;EACR;;;;EA4CD4D,sBAME5E,MAGAC,MAMAc,MAIe;AACf,WAAO,KAAK0D,mBAAmBzE,MAAaC,MAAac,IAAlD,EACJwC,KAAKC,IADD,EAEJC,MAAMD,IAFF;EAGR;EAEDjE,wBAA0C;AACxC,WAAO,KAAKd,cAAcc,sBAAnB;EACR;EAEDiC,gBAA4B;AAC1B,WAAO,KAAKjD;EACb;EAEDsG,mBAAkC;AAChC,WAAO,KAAKpG;EACb;EAEDqG,YAAoB;AAClB,WAAO,KAAKnG;EACb;EAEDoG,oBAAoC;AAClC,WAAO,KAAKlG;EACb;EAEDmG,kBAAkBpD,SAA+B;AAC/C,SAAK/C,iBAAiB+C;EACvB;EAEDqD,iBACEvE,UACAkB,SACM;AACN,UAAMsD,SAAS,KAAKpG,cAAc6B,KAC/BwE,OAAMC,aAAa1E,QAAD,MAAe0E,aAAaD,EAAEzE,QAAH,CADjC;AAGf,QAAIwE,QAAQ;AACVA,aAAOrG,iBAAiB+C;IACzB,OAAM;AACL,WAAK9C,cAAcuG,KAAK;QAAE3E;QAAU7B,gBAAgB+C;OAApD;IACD;EACF;EAED0D,iBACE5E,UAC2D;AAC3D,QAAI,CAACA,UAAU;AACb,aAAOZ;IACR;AAGD,UAAMyF,wBAAwB,KAAKzG,cAAc6B,KAAMwE,OACrDK,gBAAgB9E,UAAUyE,EAAEzE,QAAb,CADa;AAK9B,QAAI+E,MAAuC;AAEzC,YAAMC,mBAAmB,KAAK5G,cAAc+E,OAAQsB,OAClDK,gBAAgB9E,UAAUyE,EAAEzE,QAAb,CADQ;AAIzB,UAAIgF,iBAAiBpF,SAAS,GAAG;AAC/B,aAAK3B,OAAOM,MAAZ,0DAC0D0G,KAAKC,UAC3DlF,QADsD,IAD1D,gNAAA;MAKD;IACF;AAED,WAAO6E,yBAAP,OAAA,SAAOA,sBAAuB1G;EAC/B;EAEDgH,oBACEC,aACAlE,SACM;AACN,UAAMsD,SAAS,KAAKnG,iBAAiB4B,KAClCwE,OAAMC,aAAaU,WAAD,MAAkBV,aAAaD,EAAEW,WAAH,CADpC;AAGf,QAAIZ,QAAQ;AACVA,aAAOrG,iBAAiB+C;IACzB,OAAM;AACL,WAAK7C,iBAAiBsG,KAAK;QAAES;QAAajH,gBAAgB+C;OAA1D;IACD;EACF;EAEDmE,oBACED,aACyD;AACzD,QAAI,CAACA,aAAa;AAChB,aAAOhG;IACR;AAGD,UAAMyF,wBAAwB,KAAKxG,iBAAiB4B,KAAMwE,OACxDK,gBAAgBM,aAAaX,EAAEW,WAAhB,CADa;AAK9B,QAAIL,MAAuC;AAEzC,YAAMC,mBAAmB,KAAK3G,iBAAiB8E,OAAQsB,OACrDK,gBAAgBM,aAAaX,EAAEW,WAAhB,CADQ;AAIzB,UAAIJ,iBAAiBpF,SAAS,GAAG;AAC/B,aAAK3B,OAAOM,MAAZ,6DAC6D0G,KAAKC,UAC9DE,WADyD,IAD7D,yNAAA;MAKD;IACF;AAED,WAAOP,yBAAP,OAAA,SAAOA,sBAAuB1G;EAC/B;EAEDoD,oBAOEL,SAeA;AACA,QAAIA,WAAJ,QAAIA,QAASoE,YAAY;AACvB,aAAOpE;IAOR;AAED,UAAMI,mBAAmB,+DACpB,KAAKnD,eAAeoH,UACpB,KAAKX,iBAAiB1D,WAAtB,OAAA,SAAsBA,QAASlB,QAA/B,IACAkB,UAHoB;MAIvBoE,YAAY;;AAGd,QAAI,CAAChE,iBAAiBkE,aAAalE,iBAAiBtB,UAAU;AAC5DsB,uBAAiBkE,YAAYC,sBAC3BnE,iBAAiBtB,UACjBsB,gBAFgD;IAInD;AAGD,QAAI,OAAOA,iBAAiBoE,uBAAuB,aAAa;AAC9DpE,uBAAiBoE,qBACfpE,iBAAiBqE,gBAAgB;IACpC;AACD,QAAI,OAAOrE,iBAAiBsE,qBAAqB,aAAa;AAC5DtE,uBAAiBsE,mBAAmB,CAAC,CAACtE,iBAAiBuE;IACxD;AAED,WAAOvE;EAOR;EAEDwE,uBACE5E,SACG;AACH,QAAIA,WAAJ,QAAIA,QAASoE,YAAY;AACvB,aAAOpE;IACR;AACD,WAAO,+DACF,KAAK/C,eAAe4H,YACpB,KAAKV,oBAAoBnE,WAAzB,OAAA,SAAyBA,QAASkE,WAAlC,IACAlE,UAHE;MAILoE,YAAY;;EAEf;EAEDU,QAAc;AACZ,SAAKnI,WAAWmI,MAAhB;AACA,SAAKjI,cAAciI,MAAnB;EACD;AAn4BsB;;;AClBlB,IAAMC,gBAAN,cAMGC,aAAmD;EA8B3DC,YACEC,QACAC,SAOA;AACA,UAAA;AAEA,SAAKD,SAASA;AACd,SAAKC,UAAUA;AACf,SAAKC,eAAe,oBAAIC,IAAJ;AACpB,SAAKC,cAAc;AACnB,SAAKC,YAAL;AACA,SAAKC,WAAWL,OAAhB;EACD;EAESI,cAAoB;AAC5B,SAAKE,SAAS,KAAKA,OAAOC,KAAK,IAAjB;AACd,SAAKC,UAAU,KAAKA,QAAQD,KAAK,IAAlB;EAChB;EAESE,cAAoB;AAC5B,QAAI,KAAKC,UAAUC,SAAS,GAAG;AAC7B,WAAKC,aAAaC,YAAY,IAA9B;AAEA,UAAIC,mBAAmB,KAAKF,cAAc,KAAKZ,OAAzB,GAAmC;AACvD,aAAKe,aAAL;MACD;AAED,WAAKC,aAAL;IACD;EACF;EAESC,gBAAsB;AAC9B,QAAI,CAAC,KAAKC,aAAL,GAAqB;AACxB,WAAKC,QAAL;IACD;EACF;EAEDC,yBAAkC;AAChC,WAAOC,cACL,KAAKT,cACL,KAAKZ,SACL,KAAKA,QAAQsB,kBAHK;EAKrB;EAEDC,2BAAoC;AAClC,WAAOF,cACL,KAAKT,cACL,KAAKZ,SACL,KAAKA,QAAQwB,oBAHK;EAKrB;EAEDL,UAAgB;AACd,SAAKT,YAAY,oBAAIR,IAAJ;AACjB,SAAKuB,kBAAL;AACA,SAAKC,qBAAL;AACA,SAAKd,aAAae,eAAe,IAAjC;EACD;EAEDtB,WACEL,SAOA4B,eACM;AACN,UAAMC,cAAc,KAAK7B;AACzB,UAAM8B,YAAY,KAAKlB;AAEvB,SAAKZ,UAAU,KAAKD,OAAOgC,oBAAoB/B,OAAhC;AAEf,QAEE,QAAOA,WAAP,OAAA,SAAOA,QAASgC,iBAAgB,aAChC;AACA,WAAKjC,OACFkC,UADH,EAEGC,MAFH,wLAAA;IAKD;AAED,QAAI,CAACC,oBAAoBN,aAAa,KAAK7B,OAAnB,GAA6B;AACnD,WAAKD,OAAOqC,cAAZ,EAA4BC,OAAO;QACjCC,MAAM;QACNC,OAAO,KAAK3B;QACZ4B,UAAU;OAHZ;IAKD;AAED,QACE,OAAO,KAAKxC,QAAQyC,YAAY,eAChC,OAAO,KAAKzC,QAAQyC,YAAY,WAChC;AACA,YAAM,IAAIC,MAAM,kCAAV;IACP;AAGD,QAAI,CAAC,KAAK1C,QAAQ2C,UAAU;AAC1B,WAAK3C,QAAQ2C,WAAWd,YAAYc;IACrC;AAED,SAAKC,YAAL;AAEA,UAAMC,UAAU,KAAK3B,aAAL;AAGhB,QACE2B,WACAC,sBACE,KAAKlC,cACLkB,WACA,KAAK9B,SACL6B,WAJmB,GAMrB;AACA,WAAKd,aAAL;IACD;AAGD,SAAKgC,aAAanB,aAAlB;AAGA,QACEiB,YACC,KAAKjC,iBAAiBkB,aACrB,KAAK9B,QAAQyC,YAAYZ,YAAYY,WACrC,KAAKzC,QAAQgD,cAAcnB,YAAYmB,YACzC;AACA,WAAKC,mBAAL;IACD;AAED,UAAMC,sBAAsB,KAAKC,uBAAL;AAG5B,QACEN,YACC,KAAKjC,iBAAiBkB,aACrB,KAAK9B,QAAQyC,YAAYZ,YAAYY,WACrCS,wBAAwB,KAAKE,yBAC/B;AACA,WAAKC,sBAAsBH,mBAA3B;IACD;EACF;EAEDI,oBACEtD,SAOoC;AACpC,UAAMuC,QAAQ,KAAKxC,OAAOqC,cAAZ,EAA4BmB,MAAM,KAAKxD,QAAQC,OAA/C;AAEd,UAAMwD,SAAS,KAAKC,aAAalB,OAAOvC,OAAzB;AAEf,QAAI0D,sCAAsC,MAAMF,QAAQxD,OAAf,GAAyB;AAiBhE,WAAK2D,gBAAgBH;AACrB,WAAKI,uBAAuB,KAAK5D;AACjC,WAAK6D,qBAAqB,KAAKjD,aAAakD;IAC7C;AACD,WAAON;EACR;EAEDO,mBAAuD;AACrD,WAAO,KAAKJ;EACb;EAEDK,YACER,QACoC;AACpC,UAAMS,gBAAgB,CAAA;AAEtBC,WAAOC,KAAKX,MAAZ,EAAoBY,QAASC,SAAQ;AACnCH,aAAOI,eAAeL,eAAeI,KAAK;QACxCE,cAAc;QACdC,YAAY;QACZC,KAAK,MAAM;AACT,eAAKxE,aAAayE,IAAIL,GAAtB;AACA,iBAAOb,OAAOa,GAAD;QACd;OANH;KADF;AAWA,WAAOJ;EACR;EAEDU,kBAAsE;AACpE,WAAO,KAAK/D;EACb;EAEDN,SAAe;AACb,SAAKP,OAAOqC,cAAZ,EAA4B9B,OAAO,KAAKM,YAAxC;EACD;EAEDJ,QAAmB,KAGiC,CAAA,GAElD;AALiB,iBACjBoE;;QADiB,IAEd5E,oBAFc,IAEdA;MADH4E;;AAKA,WAAO,KAAKC,MAAM,iCACb7E,UADa;MAEhB8E,MAAM;QAAEF;MAAF;IAFU,EAAX;EAIR;EAEDG,gBACE/E,SAO6C;AAC7C,UAAMgF,mBAAmB,KAAKjF,OAAOgC,oBAAoB/B,OAAhC;AAEzB,UAAMuC,QAAQ,KAAKxC,OAChBqC,cADW,EAEXmB,MAAM,KAAKxD,QAAQiF,gBAFR;AAGdzC,UAAM0C,uBAAuB;AAE7B,WAAO1C,MAAMsC,MAAN,EAAcK,KAAK,MAAM,KAAKzB,aAAalB,OAAOyC,gBAAzB,CAAzB;EACR;EAESH,MACRM,cAC6C;AAAA,QAAA;AAC7C,WAAO,KAAKpE,aAAa,iCACpBoE,eADoB;MAEvBC,gBAAeD,wBAAAA,aAAaC,kBAAiB,OAAA,wBAAA;MAFxC,EAGJF,KAAK,MAAM;AACZ,WAAKnC,aAAL;AACA,aAAO,KAAKY;IACb,CANM;EAOR;EAEO5C,aACNoE,cACiC;AAEjC,SAAKvC,YAAL;AAGA,QAAIyC,UAA2C,KAAKzE,aAAaiE,MAC/D,KAAK7E,SACLmF,YAF6C;AAK/C,QAAI,EAACA,gBAAD,QAACA,aAAcG,eAAc;AAC/BD,gBAAUA,QAAQE,MAAMC,IAAd;IACX;AAED,WAAOH;EACR;EAEOpC,qBAA2B;AACjC,SAAKxB,kBAAL;AAEA,QACEgE,YACA,KAAK9B,cAAc+B,WACnB,CAACC,eAAe,KAAK3F,QAAQgD,SAAd,GACf;AACA;IACD;AAED,UAAM4C,OAAOC,eACX,KAAKlC,cAAcmC,eACnB,KAAK9F,QAAQgD,SAFY;AAO3B,UAAM+C,UAAUH,OAAO;AAEvB,SAAKI,iBAAiBC,WAAW,MAAM;AACrC,UAAI,CAAC,KAAKtC,cAAc+B,SAAS;AAC/B,aAAK3C,aAAL;MACD;OACAgD,OAJ6B;EAKjC;EAEO5C,yBAAyB;AAAA,QAAA;AAC/B,WAAO,OAAO,KAAKnD,QAAQkG,oBAAoB,aAC3C,KAAKlG,QAAQkG,gBAAgB,KAAKvC,cAAcwC,MAAM,KAAKvF,YAA3D,KACA,wBAAA,KAAKZ,QAAQkG,oBAFV,OAAA,wBAE6B;EACrC;EAEO7C,sBAAsB+C,cAAoC;AAChE,SAAK1E,qBAAL;AAEA,SAAK0B,yBAAyBgD;AAE9B,QACEX,YACA,KAAKzF,QAAQyC,YAAY,SACzB,CAACkD,eAAe,KAAKvC,sBAAN,KACf,KAAKA,2BAA2B,GAChC;AACA;IACD;AAED,SAAKiD,oBAAoBC,YAAY,MAAM;AACzC,UACE,KAAKtG,QAAQuG,+BACbC,aAAaC,UAAb,GACA;AACA,aAAK1F,aAAL;MACD;OACA,KAAKqC,sBAP4B;EAQrC;EAEOpC,eAAqB;AAC3B,SAAKiC,mBAAL;AACA,SAAKI,sBAAsB,KAAKF,uBAAL,CAA3B;EACD;EAEO1B,oBAA0B;AAChC,QAAI,KAAKuE,gBAAgB;AACvBU,mBAAa,KAAKV,cAAN;AACZ,WAAKA,iBAAiBW;IACvB;EACF;EAEOjF,uBAA6B;AACnC,QAAI,KAAK2E,mBAAmB;AAC1BO,oBAAc,KAAKP,iBAAN;AACb,WAAKA,oBAAoBM;IAC1B;EACF;EAESlD,aACRlB,OACAvC,SAOoC;AACpC,UAAM8B,YAAY,KAAKlB;AACvB,UAAMiB,cAAc,KAAK7B;AACzB,UAAM6G,aAAa,KAAKlD;AAGxB,UAAMmD,kBAAkB,KAAKjD;AAC7B,UAAMkD,oBAAoB,KAAKnD;AAC/B,UAAMoD,cAAczE,UAAUT;AAC9B,UAAMmF,oBAAoBD,cACtBzE,MAAMuB,QACN,KAAKoD;AACT,UAAMC,kBAAkBH,cACpB,KAAKrD,gBACL,KAAKyD;AAET,UAAM;MAAEtD;IAAF,IAAYvB;AAClB,QAAI;MAAEuD;MAAe5D;MAAOmF;MAAgBC;MAAaC;IAArD,IAAgEzD;AACpE,QAAI0D,iBAAiB;AACrB,QAAIC,oBAAoB;AACxB,QAAItB;AAGJ,QAAInG,QAAQ0H,oBAAoB;AAC9B,YAAM7E,UAAU,KAAK3B,aAAL;AAEhB,YAAMyG,eAAe,CAAC9E,WAAW/B,mBAAmByB,OAAOvC,OAAR;AAEnD,YAAM4H,kBACJ/E,WAAWC,sBAAsBP,OAAOT,WAAW9B,SAAS6B,WAA5B;AAElC,UAAI8F,gBAAgBC,iBAAiB;AACnCN,sBAAcO,SAAStF,MAAMvC,QAAQ8H,WAAf,IAClB,aACA;AACJ,YAAI,CAAChC,eAAe;AAClByB,mBAAS;QACV;MACF;AACD,UAAIvH,QAAQ0H,uBAAuB,eAAe;AAChDJ,sBAAc;MACf;IACF;AAGD,QACEtH,QAAQ+H,oBACR,CAACjE,MAAMgC,iBACPqB,mBAFA,QAEAA,gBAAiBa,aACjBT,WAAW,SACX;AACApB,aAAOgB,gBAAgBhB;AACvBL,sBAAgBqB,gBAAgBrB;AAChCyB,eAASJ,gBAAgBI;AACzBC,uBAAiB;IAClB,WAEQxH,QAAQiI,UAAU,OAAOnE,MAAMqC,SAAS,aAAa;AAE5D,UACEU,cACA/C,MAAMqC,UAASW,mBAAAA,OAAAA,SAAAA,gBAAiBX,SAChCnG,QAAQiI,WAAW,KAAKC,UACxB;AACA/B,eAAO,KAAKgC;MACb,OAAM;AACL,YAAI;AACF,eAAKD,WAAWlI,QAAQiI;AACxB9B,iBAAOnG,QAAQiI,OAAOnE,MAAMqC,IAArB;AACPA,iBAAOiC,YAAYvB,cAAAA,OAAAA,SAAAA,WAAYV,MAAMA,MAAMnG,OAAzB;AAClB,eAAKmI,eAAehC;AACpB,eAAKhG,cAAc;iBACZA,aAAa;AACpB,cAAIkI,MAAuC;AACzC,iBAAKtI,OAAOkC,UAAZ,EAAwBC,MAAM/B,WAA9B;UACD;AACD,eAAKA,cAAcA;QACpB;MACF;IACF,OAEI;AACHgG,aAAOrC,MAAMqC;IACd;AAGD,QACE,OAAOnG,QAAQsI,oBAAoB,eACnC,OAAOnC,SAAS,eAChBoB,WAAW,WACX;AACA,UAAIe;AAGJ,UACEzB,cAAA,QAAAA,WAAYY,qBACZzH,QAAQsI,qBAAoBvB,qBAA5B,OAAA,SAA4BA,kBAAmBuB,kBAC/C;AACAA,0BAAkBzB,WAAWV;MAC9B,OAAM;AACLmC,0BACE,OAAOtI,QAAQsI,oBAAoB,aAC9BtI,QAAQsI,gBAAT,IACAtI,QAAQsI;AACd,YAAItI,QAAQiI,UAAU,OAAOK,oBAAoB,aAAa;AAC5D,cAAI;AACFA,8BAAkBtI,QAAQiI,OAAOK,eAAf;AAClB,iBAAKnI,cAAc;mBACZA,aAAa;AACpB,gBAAIkI,MAAuC;AACzC,mBAAKtI,OAAOkC,UAAZ,EAAwBC,MAAM/B,WAA9B;YACD;AACD,iBAAKA,cAAcA;UACpB;QACF;MACF;AAED,UAAI,OAAOmI,oBAAoB,aAAa;AAC1Cf,iBAAS;AACTpB,eAAOiC,YAAYvB,cAAAA,OAAAA,SAAAA,WAAYV,MAAMmC,iBAAiBtI,OAApC;AAClByH,4BAAoB;MACrB;IACF;AAED,QAAI,KAAKtH,aAAa;AACpB+B,cAAQ,KAAK/B;AACbgG,aAAO,KAAKgC;AACZd,uBAAiBkB,KAAKC,IAAL;AACjBjB,eAAS;IACV;AAED,UAAMkB,aAAanB,gBAAgB;AACnC,UAAMoB,YAAYnB,WAAW;AAC7B,UAAMoB,WAAUpB,WAAW;AAE3B,UAAM/D,SAAiD;MACrD+D;MACAD;MACAoB;MACAV,WAAWT,WAAW;MACtBoB,SAAAA;MACAC,kBAAkBF,aAAaD;MAC/BtC;MACAL;MACA5D;MACAmF;MACAwB,cAAc/E,MAAMgF;MACpBC,eAAejF,MAAMkF;MACrBC,kBAAkBnF,MAAMmF;MACxBC,WAAWpF,MAAMqF,kBAAkB,KAAKrF,MAAMmF,mBAAmB;MACjEG,qBACEtF,MAAMqF,kBAAkBlC,kBAAkBkC,mBAC1CrF,MAAMmF,mBAAmBhC,kBAAkBgC;MAC7CR;MACAY,cAAcZ,cAAc,CAACC;MAC7BY,gBAAgBX,YAAW7E,MAAMgC,kBAAkB;MACnDyD,UAAUjC,gBAAgB;MAC1BG;MACAD;MACAgC,gBAAgBb,YAAW7E,MAAMgC,kBAAkB;MACnDJ,SAASA,QAAQnD,OAAOvC,OAAR;MAChBQ,SAAS,KAAKA;MACdF,QAAQ,KAAKA;;AAGf,WAAOkD;EACR;EAEDT,aAAanB,eAAqC;AAChD,UAAMiF,aAAa,KAAKlD;AAIxB,UAAM8F,aAAa,KAAKhG,aAAa,KAAK7C,cAAc,KAAKZ,OAA1C;AACnB,SAAK6D,qBAAqB,KAAKjD,aAAakD;AAC5C,SAAKF,uBAAuB,KAAK5D;AAGjC,QAAImC,oBAAoBsH,YAAY5C,UAAb,GAA0B;AAC/C;IACD;AAED,SAAKlD,gBAAgB8F;AAGrB,UAAMC,uBAAsC;MAAEC,OAAO;;AAErD,UAAMC,wBAAwB,MAAe;AAC3C,UAAI,CAAC/C,YAAY;AACf,eAAO;MACR;AAED,YAAM;QAAEgD;MAAF,IAA0B,KAAK7J;AACrC,YAAM8J,2BACJ,OAAOD,wBAAwB,aAC3BA,oBAAmB,IACnBA;AAEN,UACEC,6BAA6B,SAC5B,CAACA,4BAA4B,CAAC,KAAK7J,aAAaU,MACjD;AACA,eAAO;MACR;AAED,YAAMoJ,gBAAgB,IAAI7J,IACxB4J,4BADoB,OACpBA,2BAA4B,KAAK7J,YADb;AAItB,UAAI,KAAKD,QAAQgK,kBAAkB;AACjCD,sBAAcrF,IAAI,OAAlB;MACD;AAED,aAAOR,OAAOC,KAAK,KAAKR,aAAjB,EAAgCsG,KAAM5F,SAAQ;AACnD,cAAM6F,WAAW7F;AACjB,cAAM8F,UAAU,KAAKxG,cAAcuG,QAAnB,MAAiCrD,WAAWqD,QAAD;AAC3D,eAAOC,WAAWJ,cAAcK,IAAIF,QAAlB;MACnB,CAJM;;AAOT,SAAItI,iBAAA,OAAA,SAAAA,cAAelB,eAAc,SAASkJ,sBAAqB,GAAI;AACjEF,2BAAqBhJ,YAAY;IAClC;AAED,SAAK2B,OAAO,kCAAKqH,uBAAyB9H,cAA1C;EACD;EAEOgB,cAAoB;AAC1B,UAAML,QAAQ,KAAKxC,OAAOqC,cAAZ,EAA4BmB,MAAM,KAAKxD,QAAQ,KAAKC,OAApD;AAEd,QAAIuC,UAAU,KAAK3B,cAAc;AAC/B;IACD;AAED,UAAMkB,YAAY,KAAKlB;AAGvB,SAAKA,eAAe2B;AACpB,SAAK2E,2BAA2B3E,MAAMuB;AACtC,SAAKsD,sBAAsB,KAAKzD;AAEhC,QAAI,KAAKzC,aAAL,GAAqB;AACvBY,mBAAS,OAATA,SAAAA,UAAWH,eAAe,IAA1B;AACAY,YAAM1B,YAAY,IAAlB;IACD;EACF;EAEDwJ,cAAcC,QAAqC;AACjD,UAAM1I,gBAA+B,CAAA;AAErC,QAAI0I,OAAOhI,SAAS,WAAW;AAC7BV,oBAAc2I,YAAY,CAACD,OAAOE;IACnC,WAAUF,OAAOhI,SAAS,WAAW,CAACmI,iBAAiBH,OAAOpI,KAAR,GAAgB;AACrEN,oBAAc8I,UAAU;IACzB;AAED,SAAK3H,aAAanB,aAAlB;AAEA,QAAI,KAAKV,aAAL,GAAqB;AACvB,WAAKF,aAAL;IACD;EACF;EAEOqB,OAAOT,eAAoC;AACjD+I,kBAAcC,MAAM,MAAM;AAExB,UAAIhJ,cAAc2I,WAAW;AAAA,YAAA,uBAAA,eAAA,uBAAA;AAC3B,SAAA,yBAAA,gBAAA,KAAKvK,SAAQuK,cAAb,OAAA,SAAA,sBAAA,KAAA,eAAyB,KAAK5G,cAAcwC,IAA5C;AACA,SAAKnG,yBAAAA,iBAAAA,KAAAA,SAAQ6K,cAAb,OAAA,SAAA,sBAAA,KAAA,gBAAyB,KAAKlH,cAAcwC,MAAO,IAAnD;MACD,WAAUvE,cAAc8I,SAAS;AAAA,YAAA,uBAAA,gBAAA,wBAAA;AAChC,SAAA,yBAAA,iBAAA,KAAK1K,SAAQ0K,YAAb,OAAA,SAAA,sBAAA,KAAA,gBAAuB,KAAK/G,cAAczB,KAA1C;AACA,SAAKlC,0BAAAA,iBAAAA,KAAAA,SAAQ6K,cAAb,OAAA,SAAA,uBAAA,KAAA,gBAAyBlE,QAAW,KAAKhD,cAAczB,KAAvD;MACD;AAGD,UAAIN,cAAclB,WAAW;AAC3B,aAAKA,UAAU0D,QAAQ,CAAC;UAAE0G;QAAF,MAAiB;AACvCA,mBAAS,KAAKnH,aAAN;SADV;MAGD;AAGD,UAAI/B,cAAc+H,OAAO;AACvB,aAAK5J,OAAOqC,cAAZ,EAA4BC,OAAO;UACjCE,OAAO,KAAK3B;UACZ0B,MAAM;SAFR;MAID;KAvBH;EAyBD;AAjrB0D;AAorB7D,SAASyI,kBACPxI,OACAvC,SACS;AACT,SACEA,QAAQyC,YAAY,SACpB,CAACF,MAAMuB,MAAMgC,iBACb,EAAEvD,MAAMuB,MAAMyD,WAAW,WAAWvH,QAAQgL,iBAAiB;AAEhE;AAED,SAASlK,mBACPyB,OACAvC,SACS;AACT,SACE+K,kBAAkBxI,OAAOvC,OAAR,KAChBuC,MAAMuB,MAAMgC,gBAAgB,KAC3BzE,cAAckB,OAAOvC,SAASA,QAAQiL,cAAzB;AAElB;AAED,SAAS5J,cACPkB,OACAvC,SACAkL,OAGA;AACA,MAAIlL,QAAQyC,YAAY,OAAO;AAC7B,UAAM0I,QAAQ,OAAOD,UAAU,aAAaA,MAAM3I,KAAD,IAAU2I;AAE3D,WAAOC,UAAU,YAAaA,UAAU,SAASzF,QAAQnD,OAAOvC,OAAR;EACzD;AACD,SAAO;AACR;AAED,SAAS8C,sBACPP,OACAT,WACA9B,SACA6B,aACS;AACT,SACE7B,QAAQyC,YAAY,UACnBF,UAAUT,aAAaD,YAAYY,YAAY,WAC/C,CAACzC,QAAQoL,YAAY7I,MAAMuB,MAAMyD,WAAW,YAC7C7B,QAAQnD,OAAOvC,OAAR;AAEV;AAED,SAAS0F,QACPnD,OACAvC,SACS;AACT,SAAOuC,MAAM8I,cAAcrL,QAAQgD,SAA5B;AACR;AAID,SAASU,sCAOPlB,UACA8I,kBACAtL,SAOA;AAOA,MAAIA,QAAQ+H,kBAAkB;AAC5B,WAAO;EACR;AAID,MAAI/H,QAAQsI,oBAAoB3B,QAAW;AAIzC,WAAO2E,iBAAiB7D;EACzB;AAID,MAAI,CAACtF,oBAAoBK,SAASuB,iBAAT,GAA6BuH,gBAA9B,GAAiD;AACvE,WAAO;EACR;AAGD,SAAO;AACR;;;AC5zBM,IAAMC,kBAAN,cAA8BC,aAAsC;EAOzEC,YAAYC,QAAqBC,SAAkC;AACjE,UAAA;AAEA,SAAKD,SAASA;AACd,SAAKC,UAAU,CAAA;AACf,SAAKC,SAAS,CAAA;AACd,SAAKC,YAAY,CAAA;AACjB,SAAKC,eAAe,CAAA;AAEpB,QAAIH,SAAS;AACX,WAAKI,WAAWJ,OAAhB;IACD;EACF;EAESK,cAAoB;AAC5B,QAAI,KAAKC,UAAUC,SAAS,GAAG;AAC7B,WAAKL,UAAUM,QAASC,cAAa;AACnCA,iBAASC,UAAWT,YAAW;AAC7B,eAAKU,SAASF,UAAUR,MAAxB;SADF;OADF;IAKD;EACF;EAESW,gBAAsB;AAC9B,QAAI,CAAC,KAAKN,UAAUC,MAAM;AACxB,WAAKM,QAAL;IACD;EACF;EAEDA,UAAgB;AACd,SAAKP,YAAY,oBAAIQ,IAAJ;AACjB,SAAKZ,UAAUM,QAASC,cAAa;AACnCA,eAASI,QAAT;KADF;EAGD;EAEDT,WACEJ,SACAe,eACM;AACN,SAAKf,UAAUA;AAEfgB,kBAAcC,MAAM,MAAM;AACxB,YAAMC,gBAAgB,KAAKhB;AAE3B,YAAMiB,qBAAqB,KAAKC,sBAAsB,KAAKpB,OAAhC;AAG3BmB,yBAAmBX,QAASa,WAC1BA,MAAMZ,SAASa,WAAWD,MAAME,uBAAuBR,aAAvD,CADF;AAIA,YAAMS,eAAeL,mBAAmBM,IAAKJ,WAAUA,MAAMZ,QAAxC;AACrB,YAAMiB,kBAAkBC,OAAOC,YAC7BJ,aAAaC,IAAKhB,cAAa,CAACA,SAASoB,QAAQC,WAAWrB,QAA7B,CAA/B,CADsB;AAGxB,YAAMsB,YAAYP,aAAaC,IAAKhB,cAClCA,SAASuB,iBAAT,CADgB;AAIlB,YAAMC,iBAAiBT,aAAaU,KAClC,CAACzB,UAAU0B,UAAU1B,aAAaS,cAAciB,KAAD,CAD1B;AAGvB,UAAIjB,cAAckB,WAAWZ,aAAaY,UAAU,CAACH,gBAAgB;AACnE;MACD;AAED,WAAK/B,YAAYsB;AACjB,WAAKrB,eAAeuB;AACpB,WAAKzB,SAAS8B;AAEd,UAAI,CAAC,KAAKM,aAAL,GAAqB;AACxB;MACD;AAEDC,iBAAWpB,eAAeM,YAAhB,EAA8BhB,QAASC,cAAa;AAC5DA,iBAASI,QAAT;OADF;AAIAyB,iBAAWd,cAAcN,aAAf,EAA8BV,QAASC,cAAa;AAC5DA,iBAASC,UAAWT,YAAW;AAC7B,eAAKU,SAASF,UAAUR,MAAxB;SADF;OADF;AAMA,WAAKsC,OAAL;KA3CF;EA6CD;EAEDP,mBAA0C;AACxC,WAAO,KAAK/B;EACb;EAEDuC,aAAa;AACX,WAAO,KAAKtC,UAAUuB,IAAKhB,cAAaA,SAASgC,gBAAT,CAAjC;EACR;EAEDC,eAAe;AACb,WAAO,KAAKxC;EACb;EAEDyC,oBAAoB3C,SAAwD;AAC1E,WAAO,KAAKoB,sBAAsBpB,OAA3B,EAAoCyB,IAAKJ,WAC9CA,MAAMZ,SAASkC,oBAAoBtB,MAAME,qBAAzC,CADK;EAGR;EAEOH,sBACNpB,SACsB;AACtB,UAAMkB,gBAAgB,KAAKhB;AAC3B,UAAM0C,mBAAmB,IAAIC,IAC3B3B,cAAcO,IAAKhB,cAAa,CAACA,SAASoB,QAAQC,WAAWrB,QAA7B,CAAhC,CADuB;AAIzB,UAAMc,wBAAwBvB,QAAQyB,IAAKI,aACzC,KAAK9B,OAAO+C,oBAAoBjB,OAAhC,CAD4B;AAI9B,UAAMkB,oBACJxB,sBAAsByB,QAASC,sBAAqB;AAClD,YAAM5B,QAAQuB,iBAAiBM,IAAID,iBAAiBnB,SAAtC;AACd,UAAIT,SAAS,MAAM;AACjB,eAAO,CAAC;UAAEE,uBAAuB0B;UAAkBxC,UAAUY;QAArD,CAAD;MACR;AACD,aAAO,CAAA;IACR,CAND;AAQF,UAAM8B,qBAAqB,IAAIrC,IAC7BiC,kBAAkBtB,IAAKJ,WAAUA,MAAME,sBAAsBO,SAA7D,CADyB;AAG3B,UAAMsB,mBAAmB7B,sBAAsB8B,OAC5CJ,sBAAqB,CAACE,mBAAmBG,IAAIL,iBAAiBnB,SAAxC,CADA;AAIzB,UAAMyB,uBAAuB,IAAIzC,IAC/BiC,kBAAkBtB,IAAKJ,WAAUA,MAAMZ,QAAvC,CAD2B;AAG7B,UAAM+C,qBAAqBtC,cAAcmC,OACtCI,kBAAiB,CAACF,qBAAqBD,IAAIG,YAAzB,CADM;AAI3B,UAAMC,cAAe7B,aAAiD;AACpE,YAAMoB,mBAAmB,KAAKlD,OAAO+C,oBAAoBjB,OAAhC;AACzB,YAAM8B,kBAAkB,KAAKxD,aAAa8C,iBAAiBnB,SAAnC;AACxB,aAAO6B,mBAAAA,OAAAA,kBAAmB,IAAIC,cAAc,KAAK7D,QAAQkD,gBAA/B;;AAG5B,UAAMY,uBAA6CT,iBAAiB3B,IAClE,CAACI,SAASM,UAAU;AAClB,UAAIN,QAAQiC,kBAAkB;AAE5B,cAAMC,yBAAyBP,mBAAmBrB,KAAD;AACjD,YAAI4B,2BAA2BC,QAAW;AACxC,iBAAO;YACLzC,uBAAuBM;YACvBpB,UAAUsD;;QAEb;MACF;AACD,aAAO;QACLxC,uBAAuBM;QACvBpB,UAAUiD,YAAY7B,OAAD;;IAExB,CAhBgD;AAmBnD,UAAMoC,8BAA8B,CAClCC,GACAC,MAEA5C,sBAAsB6C,QAAQF,EAAE3C,qBAAhC,IACAA,sBAAsB6C,QAAQD,EAAE5C,qBAAhC;AAEF,WAAOwB,kBACJsB,OAAOR,oBADH,EAEJS,KAAKL,2BAFD;EAGR;EAEOtD,SAASF,UAAyBR,QAAmC;AAC3E,UAAMkC,QAAQ,KAAKjC,UAAUkE,QAAQ3D,QAAvB;AACd,QAAI0B,UAAU,IAAI;AAChB,WAAKlC,SAASsE,UAAU,KAAKtE,QAAQkC,OAAOlC,MAArB;AACvB,WAAKsC,OAAL;IACD;EACF;EAEOA,SAAe;AACrBvB,kBAAcC,MAAM,MAAM;AACxB,WAAKX,UAAUE,QAAQ,CAAC;QAAEgE;MAAF,MAAiB;AACvCA,iBAAS,KAAKvE,MAAN;OADV;KADF;EAKD;AAzMwE;;;ACSpE,IAAMwE,wBAAN,cAMGC,cAMR;;;;;EAeAC,YACEC,QACAC,SAOA;AACA,UAAMD,QAAQC,OAAd;EACD;EAESC,cAAoB;AAC5B,UAAMA,YAAN;AACA,SAAKC,gBAAgB,KAAKA,cAAcC,KAAK,IAAxB;AACrB,SAAKC,oBAAoB,KAAKA,kBAAkBD,KAAK,IAA5B;EAC1B;EAEDE,WACEL,SAOAM,eACM;AACN,UAAMD,WACJ,iCACKL,UADL;MAEEO,UAAUC,sBAAqB;IAFjC,IAIAF,aALF;EAOD;EAEDG,oBACET,SAO4C;AAC5CA,YAAQO,WAAWC,sBAAqB;AACxC,WAAO,MAAMC,oBAAoBT,OAA1B;EAIR;EAEDE,cAAc,KAAkD,CAAA,GAE9D;AAFY,iBAAEQ;;QAAF,IAAgBV,oBAAhB,IAAgBA;MAAdU;;AAGd,WAAO,KAAKC,MAAM,iCACbX,UADa;MAEhBY,MAAM;QACJC,WAAW;UAAEC,WAAW;UAAWJ;QAAxB;MADP;IAFU,EAAX;EAMR;EAEDN,kBAAkB,KAGY,CAAA,GAE5B;AALgB,iBAChBM;;QADgB,IAEbV,oBAFa,IAEbA;MADHU;;AAKA,WAAO,KAAKC,MAAM,iCACbX,UADa;MAEhBY,MAAM;QACJC,WAAW;UAAEC,WAAW;UAAYJ;QAAzB;MADP;IAFU,EAAX;EAMR;EAESK,aACRC,OACAhB,SAO4C;AAAA,QAAA,kBAAA,uBAAA,mBAAA,uBAAA,aAAA;AAC5C,UAAM;MAAEiB;IAAF,IAAYD;AAClB,UAAME,SAAS,MAAMH,aAAaC,OAAOhB,OAA1B;AAEf,UAAM;MAAEmB;MAAYC;IAAd,IAA+BF;AAErC,UAAMG,qBACJF,gBAAcF,mBAAAA,MAAMK,cAAWT,OAAAA,UAAAA,wBAAAA,iBAAAA,cAAWC,OAAAA,SAAAA,sBAAAA,eAAc;AAE1D,UAAMS,yBACJJ,gBAAcF,oBAAAA,MAAMK,cAAWT,OAAAA,UAAAA,wBAAAA,kBAAAA,cAAWC,OAAAA,SAAAA,sBAAAA,eAAc;AAE1D,WAAO,iCACFI,SADE;MAELhB,eAAe,KAAKA;MACpBE,mBAAmB,KAAKA;MACxBoB,aAAaA,YAAYxB,UAAD,cAAUiB,MAAMQ,SAAhB,OAAA,SAAU,YAAYC,KAAtB;MACxBC,iBAAiBA,gBAAgB3B,UAAD,eAAUiB,MAAMQ,SAAhB,OAAA,SAAU,aAAYC,KAAtB;MAChCL;MACAE;MACAH,cACEA,gBAAgB,CAACC,sBAAsB,CAACE;;EAE7C;AA9HD;;;ACRK,IAAMK,mBAAN,cAKGC,aAER;EAaAC,YACEC,QACAC,SACA;AACA,UAAA;AAEA,SAAKD,SAASA;AACd,SAAKE,WAAWD,OAAhB;AACA,SAAKE,YAAL;AACA,SAAKC,aAAL;EACD;EAESD,cAAoB;AAC5B,SAAKE,SAAS,KAAKA,OAAOC,KAAK,IAAjB;AACd,SAAKC,QAAQ,KAAKA,MAAMD,KAAK,IAAhB;EACd;EAEDJ,WACED,SACA;AAAA,QAAA;AACA,UAAMO,cAAc,KAAKP;AACzB,SAAKA,UAAU,KAAKD,OAAOS,uBAAuBR,OAAnC;AACf,QAAI,CAACS,oBAAoBF,aAAa,KAAKP,OAAnB,GAA6B;AACnD,WAAKD,OAAOW,iBAAZ,EAA+BC,OAAO;QACpCC,MAAM;QACNC,UAAU,KAAKC;QACfC,UAAU;OAHZ;IAKD;AACD,KAAA,wBAAA,KAAKD,oBAAL,OAAA,SAAA,sBAAsBb,WAAW,KAAKD,OAAtC;EACD;EAESgB,gBAAsB;AAC9B,QAAI,CAAC,KAAKC,aAAL,GAAqB;AAAA,UAAA;AACxB,OAAA,yBAAA,KAAKH,oBAAL,OAAA,SAAA,uBAAsBI,eAAe,IAArC;IACD;EACF;EAEDC,iBAAiBC,QAA2D;AAC1E,SAAKjB,aAAL;AAGA,UAAMkB,gBAA+B;MACnCC,WAAW;;AAGb,QAAIF,OAAOR,SAAS,WAAW;AAC7BS,oBAAcE,YAAY;IAC3B,WAAUH,OAAOR,SAAS,SAAS;AAClCS,oBAAcG,UAAU;IACzB;AAED,SAAKb,OAAOU,aAAZ;EACD;EAEDI,mBAKE;AACA,WAAO,KAAKC;EACb;EAEDpB,QAAc;AACZ,SAAKQ,kBAAkBa;AACvB,SAAKxB,aAAL;AACA,SAAKQ,OAAO;MAAEW,WAAW;KAAzB;EACD;EAEDlB,OACEwB,WACA5B,SACgB;AAChB,SAAK6B,gBAAgB7B;AAErB,QAAI,KAAKc,iBAAiB;AACxB,WAAKA,gBAAgBI,eAAe,IAApC;IACD;AAED,SAAKJ,kBAAkB,KAAKf,OAAOW,iBAAZ,EAA+BoB,MAAM,KAAK/B,QAAQ,iCACpE,KAAKC,UAD+D;MAEvE4B,WACE,OAAOA,cAAc,cAAcA,YAAY,KAAK5B,QAAQ4B;IAHS,EAAlD;AAMvB,SAAKd,gBAAgBiB,YAAY,IAAjC;AAEA,WAAO,KAAKjB,gBAAgBkB,QAArB;EACR;EAEO7B,eAAqB;AAC3B,UAAM8B,QAAQ,KAAKnB,kBACf,KAAKA,gBAAgBmB,QACrBC,iBAAe;AAEnB,UAAMC,YAAYF,MAAMG,WAAW;AACnC,UAAMC,SAKF,iCACCJ,QADD;MAEFE;MACAG,WAAWH;MACXI,WAAWN,MAAMG,WAAW;MAC5BI,SAASP,MAAMG,WAAW;MAC1BK,QAAQR,MAAMG,WAAW;MACzBhC,QAAQ,KAAKA;MACbE,OAAO,KAAKA;;AAGd,SAAKoB,gBAAgBW;EAMtB;EAEO1B,OAAOX,SAAwB;AACrC0C,kBAAcC,MAAM,MAAM;AAExB,UAAI,KAAKd,iBAAiB,KAAKZ,aAAL,GAAqB;AAC7C,YAAIjB,QAAQuB,WAAW;AAAA,cAAA,uBAAA,qBAAA,wBAAA;AACrB,WAAA,yBAAA,sBAAA,KAAKM,eAAcN,cACjB,OAAA,SAAA,sBAAA,KAAA,qBAAA,KAAKG,cAAckB,MACnB,KAAKlB,cAAcE,WACnB,KAAKF,cAAcmB,OAHrB;AAKA,WAAA,0BAAA,uBAAA,KAAKhB,eAAciB,cAAnB,OAAA,SAAA,uBAAA,KAAA,sBACE,KAAKpB,cAAckB,MACnB,MACA,KAAKlB,cAAcE,WACnB,KAAKF,cAAcmB,OAJrB;QAMD,WAAU7C,QAAQwB,SAAS;AAAA,cAAA,wBAAA,sBAAA,wBAAA;AAC1B,WAAA,0BAAA,uBAAA,KAAKK,eAAcL,YACjB,OAAA,SAAA,uBAAA,KAAA,sBAAA,KAAKE,cAAcqB,OACnB,KAAKrB,cAAcE,WACnB,KAAKF,cAAcmB,OAHrB;AAKA,WAAA,0BAAA,uBAAA,KAAKhB,eAAciB,cAAnB,OAAA,SAAA,uBAAA,KAAA,sBACEnB,QACA,KAAKD,cAAcqB,OACnB,KAAKrB,cAAcE,WACnB,KAAKF,cAAcmB,OAJrB;QAMD;MACF;AAGD,UAAI7C,QAAQsB,WAAW;AACrB,aAAKA,UAAU0B,QAAQ,CAAC;UAAEC;QAAF,MAAiB;AACvCA,mBAAS,KAAKvB,aAAN;SADV;MAGD;KAnCH;EAqCD;AA5KD;;;ACcF,SAASwB,kBAAkBC,UAAwC;AACjE,SAAO;IACLC,aAAaD,SAASE,QAAQD;IAC9BE,OAAOH,SAASG;;AAEnB;AAMD,SAASC,eAAeC,OAA+B;AACrD,SAAO;IACLF,OAAOE,MAAMF;IACbG,UAAUD,MAAMC;IAChBC,WAAWF,MAAME;;AAEpB;AAEM,SAASC,+BAA+BR,UAAoB;AACjE,SAAOA,SAASG,MAAMM;AACvB;AAEM,SAASC,4BAA4BL,OAAc;AACxD,SAAOA,MAAMF,MAAMQ,WAAW;AAC/B;AAEM,SAASC,UACdC,QACAX,UAA4B,CAAA,GACX;AACjB,QAAMY,YAAkC,CAAA;AACxC,QAAMC,UAA6B,CAAA;AAEnC,MAAIb,QAAQc,uBAAuB,OAAO;AACxC,UAAMC,0BACJf,QAAQe,2BAA2BT;AAErCK,WACGK,iBADH,EAEGC,OAFH,EAGGC,QAASpB,cAAa;AACrB,UAAIiB,wBAAwBjB,QAAD,GAAY;AACrCc,kBAAUO,KAAKtB,kBAAkBC,QAAD,CAAhC;MACD;KANL;EAQD;AAED,MAAIE,QAAQoB,qBAAqB,OAAO;AACtC,UAAMC,uBACJrB,QAAQqB,wBAAwBb;AAElCG,WACGW,cADH,EAEGL,OAFH,EAGGC,QAASf,WAAU;AAClB,UAAIkB,qBAAqBlB,KAAD,GAAS;AAC/BU,gBAAQM,KAAKjB,eAAeC,KAAD,CAA3B;MACD;KANL;EAQD;AAED,SAAO;IAAES;IAAWC;;AACrB;AAEM,SAASU,QACdZ,QACAa,iBACAxB,SACM;AACN,MAAI,OAAOwB,oBAAoB,YAAYA,oBAAoB,MAAM;AACnE;EACD;AAED,QAAMC,gBAAgBd,OAAOK,iBAAP;AACtB,QAAMU,aAAaf,OAAOW,cAAP;AAGnB,QAAMV,YAAaY,gBAAoCZ,aAAa,CAAA;AAEpE,QAAMC,UAAWW,gBAAoCX,WAAW,CAAA;AAEhED,YAAUM,QAASS,wBAAuB;AAAA,QAAA;AACxCF,kBAAcG,MACZjB,QACA,iCACKX,WAAH,OAAA,UAAA,wBAAGA,QAAS6B,mBAAT,OAAA,SAAA,sBAAyBjB,YAD9B;MAEEb,aAAa4B,mBAAmB5B;QAElC4B,mBAAmB1B,KANrB;GADF;AAWAY,UAAQK,QAAQ,CAAC;IAAEd;IAAUH;IAAOI;EAAnB,MAAmC;AAAA,QAAA;AAClD,UAAMF,QAAQuB,WAAWI,IAAIzB,SAAf;AAGd,QAAIF,OAAO;AACT,UAAIA,MAAMF,MAAM8B,gBAAgB9B,MAAM8B,eAAe;AAGnD,cAA2D9B,YAAnD+B;uBAAaC;QA3I7B,IA2ImEhC,IAAzBiC,iCAAyBjC,IAAzBiC;UAA1BF;;AACR7B,cAAMgC,SAASD,oBAAf;MACD;AACD;IACD;AAGDR,eAAWE;MACTjB;MACA,iCACKX,WAAH,OAAA,UAAA,yBAAGA,QAAS6B,mBAAT,OAAA,SAAA,uBAAyBhB,UAD9B;QAEET;QACAC;MAHF;;;MAOA,iCACKJ,QADL;QAEE+B,aAAa;;IAXjB;GAfF;AA8BD;;;ACzKD,IAAI,SAAS;;;ACGN,IAAMI,mBAAmB;AAEzB,SAASC,aAAaC,KAAc;AACzC,QAAMC,SAASD,MAAUA,MAAAA,MAAQ;AACjC,SAAUF,KAAAA,mBAAmBG;AAC9B;AAEM,SAASC,YAAWC,OAAmC;AAC5D,SAAOC,MAAMC,QAAQF,KAAd;AACR;AAEM,SAASG,cAAcH,OAAsC;AAClE,SAAOC,MAAMC,QAAQF,KAAd;AACR;AAEM,SAASI,YACdC,OACAC,QACM;AACNC,SAAOC,KAAKH,KAAZ,EAAmBI,QAASZ,SAAQ;AAClCQ,UAAMR,GAAD,IAAQS,OAAOT,GAAD;GADrB;AAGD;AAEM,SAASa,UACdV,OACAW,YACG;AACH,MAAIA,YAAY;AACd,UAAMC,SAASD,WAAWX,KAAD;AACzB,QAAIY,WAAWC,UAAaC,MAAMd,KAAD,GAAS;AACxC,aAAOY;IACR;EACF;AAED,MAAIX,MAAMC,QAAQF,KAAd,GAAsB;AACxB,WAAOA,MAAMe,IAAKC,SAAQN,UAAUM,KAAKL,UAAN,CAA5B;EACR;AAED,MAAI,OAAOX,UAAU,YAAYiB,eAAcjB,KAAD,GAAS;AACrD,UAAMkB,UAAUX,OAAOW,QAAQlB,KAAf,EAAsBe,IAAI,CAAC,CAAClB,KAAKmB,GAAN,MAAe,CACxDnB,KACAa,UAAUM,KAAKL,UAAN,CAF+C,CAA1C;AAIhB,WAAOJ,OAAOY,YAAYD,OAAnB;EACR;AAED,SAAOlB;AACR;AAEM,SAASoB,eAAkBC,KAAsB;AACtD,SAAOX,UAAUW,KAAML,SAAQ;AAC7B,QAAIF,MAAME,GAAD,GAAO;AACd,aAAOI,eAAeE,MAAMN,GAAD,CAAN;IACtB;EACF,CAJe;AAKjB;AAED,SAASC,eAAcjB,OAAiC;AACtD,MAAIO,OAAOgB,UAAUC,SAASC,KAAKzB,KAA/B,MAA0C,mBAAmB;AAC/D,WAAO;EACR;AAED,QAAMuB,YAAYhB,OAAOmB,eAAe1B,KAAtB;AAClB,SAAOuB,cAAc,QAAQA,cAAchB,OAAOgB;AACnD;AAEM,SAASI,iBACdC,mBACAC,QACS;AAET,MAAI,OAAOD,sBAAsB,YAAY;AAC3C,WAAOA,kBAAkB,GAAGC,MAAJ;EACzB;AAED,SAAO,CAAC,CAACD;AACV;;;AC7EM,SAASE,eAAeC,KAAK,IAAiB;AACnD,QAAMC,MAAMC,aAAaF,EAAD;AACxB,QAAMG,cAAcC,OAA2BH,KAAK,IAA1B;AAE1B,MAAI,CAACE,aAAa;AAAA,QAAA;AAChB,UAAME,MAAKC,sBAAAA,mBAAkB,MAArB,OAAA,SAAG,oBAAsBC;AAEjC,QAAI,CAACF,IAAI;AACP,YAAM,IAAIG,MACR,2DADI;IAGP;AAED,UAAM,IAAIA,MACR,iGADI;EAGP;AAED,SAAOL;AACR;;;ACnBM,IAAMM,cAAN,cAAyBC,WAAG;EAQjCC,KACEC,MACAC,MACgD;AAChD,UAAMC,eAAeC,eAAeH,IAAD;AACnC,UAAMI,eAAeD,eAAeF,IAAD;AACnC,QAAII,YAAWH,YAAD,GAAgB;AAC5B,aAAO,MAAMH,KAAKG,cAAcE,YAAzB;IACR;AACD,WAAO,MAAML,KAAKG,YAAX;EACR;EAWDI,QACEN,MACAC,MACS;AACT,UAAMC,eAAeC,eAAeH,IAAD;AACnC,UAAMI,eAAeD,eAAeF,IAAD;AACnC,QAAII,YAAWH,YAAD,GAAgB;AAC5B,aAAO,MAAMI,QAAQJ,cAAcE,YAA5B;IACR;AACD,WAAO,MAAME,QAAQJ,YAAd;EACR;AAvCgC;;;ACA5B,IAAMK,iBAAN,cAA4BC,cAAG;EACpCC,KACEC,SAC2D;AAC3D,WAAO,MAAMD,KAAKE,eAAeD,OAAD,CAAzB;EACR;EAEDE,QAAQF,SAAoD;AAC1D,WAAO,MAAME,QAAQD,eAAeD,OAAD,CAA5B;EACR;AATmC;;;AC0B/B,IAAMG,eAAN,cAA0BC,YAAG;EAClCC,YAAYC,SAA0C,CAAA,GAAI;AACxD,UAAMC,iBAAiBC,eAAeF,MAAD;AACrC,UAAMG,iBAAoC;MACxCC,QAAQF,eAAeD,eAAeG,MAAhB;MACtBC,gBAAgBH,eAAeD,eAAeI,cAAhB;MAC9BC,YAAYL,eAAeK,cAAc,IAAIC,YAAJ;MACzCC,eAAeP,eAAeO,iBAAiB,IAAIC,eAAJ;;AAEjD,UAAMN,cAAN;AARwD,SAW1DO,cAAcC,IAAI,KAAD;EAFhB;EASDC,WACEC,MACAC,MACQ;AACR,UAAMC,eAAeb,eAAeW,IAAD;AACnC,UAAMG,eAAed,eAAeY,IAAD;AACnC,QAAIG,YAAWF,YAAD,GAAgB;AAC5B,aAAO,MAAMH,WAAWG,cAAcC,YAA/B;IACR;AACD,WAAO,MAAMJ,WAAWG,YAAjB;EACR;EAEDG,WAAWC,SAAiD;AAC1D,WAAO,MAAMD,WAAWhB,eAAeiB,OAAD,CAA/B;EACR;EAEDC,aACEC,UACAF,SACmB;AACnB,WAAO,MAAMC,aACXlB,eAAemB,QAAD,GACdnB,eAAeiB,OAAD,CAFT;EAIR;EAQDG,eACEC,mBACiC;AACjC,UAAMC,WAAWtB,eAAeqB,iBAAD;AAC/B,QAAIN,YAAWO,QAAD,GAAY;AACxB,aAAO,MAAMF,eAAeE,QAArB;IACR;AACD,WAAO,MAAMF,eAAeE,QAArB;EACR;EAEDC,aACEJ,UACAK,SACAC,SACmB;AACnB,WAAO,MAAMF,aACXvB,eAAemB,QAAD,GACdK,SACAxB,eAAeyB,OAAD,CAHT;EAKR;EAYDC,eACEL,mBACAG,SACAC,SACiC;AACjC,UAAMZ,eAAeb,eAAeqB,iBAAD;AACnC,UAAMM,eAAe3B,eAAeyB,OAAD;AACnC,QAAIV,YAAWF,YAAD,GAAgB;AAC5B,aAAO,MAAMa,eAAeb,cAAcW,SAASG,YAA5C;IACR;AACD,WAAO,MAAMD,eACXb,cACAW,SACAG,YAHK;EAKR;EAEDC,cACET,UACAF,SACuC;AACvC,WAAO,MAAMW,cACX5B,eAAemB,QAAD,GACdnB,eAAeiB,OAAD,CAFT;EAIR;EAODY,cACElB,MACAC,MACM;AACN,UAAMC,eAAeb,eAAeW,IAAD;AACnC,QAAII,YAAWF,YAAD,GAAgB;AAC5B,aAAO,MAAMgB,cACXhB,cACAb,eAAeY,IAAD,CAFT;IAIR;AACD,WAAO,MAAMiB,cAAchB,YAApB;EACR;EAWDiB,aACEnB,MACAC,MACAmB,MACe;AACf,UAAMlB,eAAeb,eAAeW,IAAD;AACnC,UAAMG,eAAed,eAAeY,IAAD;AACnC,QAAIG,YAAWF,YAAD,GAAgB;AAC5B,aAAO,MAAMiB,aACXjB,cACAC,cACAd,eAAe+B,IAAD,CAHT;IAKR;AACD,WAAO,MAAMD,aACXjB,cACAC,YAFK;EAIR;EAWDkB,cACErB,MACAC,MACAmB,MACe;AACf,UAAMlB,eAAeb,eAAeW,IAAD;AACnC,UAAMG,eAAed,eAAeY,IAAD;AACnC,QAAIG,YAAWF,YAAD,GAAgB;AAC5B,aAAO,MAAMmB,cACXnB,cACAC,cACAd,eAAe+B,IAAD,CAHT;IAKR;AACD,WAAO,MAAMC,cACXnB,cACAC,YAFK;EAIR;EAWDmB,kBACEtB,MACAC,MACAmB,MACe;AACf,UAAMlB,eAAeb,eAAeW,IAAD;AACnC,UAAMG,eAAed,eAAeY,IAAD;AACnC,QAAIG,YAAWF,YAAD,GAAgB;AAC5B,aAAO,MAAMoB,kBACXpB,cACAC,cACAd,eAAe+B,IAAD,CAHT;IAKR;AACD,WAAO,MAAME,kBACXpB,cACAC,YAFK;EAIR;EAWDoB,eACEvB,MACAC,MACAmB,MACe;AACf,UAAMlB,eAAeb,eAAeW,IAAD;AACnC,UAAMG,eAAed,eAAeY,IAAD;AACnC,QAAIG,YAAWF,YAAD,GAAgB;AAC5B,aAAO,MAAMqB,eACXrB,cACAC,cACAd,eAAe+B,IAAD,CAHT;IAKR;AACD,WAAO,MAAMG,eACXrB,cACAC,YAFK;EAIR;EAmCDqB,WAMExB,MAGAC,MAGAmB,MAGgB;AAChB,UAAMlB,eAAeb,eAAeW,IAAD;AACnC,UAAMG,eAAed,eAAeY,IAAD;AACnC,QAAIG,YAAWF,YAAD,GAAgB;AAC5B,aAAO,MAAMsB,WACXtB,cACAC,cACAd,eAAe+B,IAAD,CAHT;IAUR;AACD,WAAO,MAAMI,WACXtB,YADK;EAGR;EAmCDuB,cAMEzB,MAGAC,MAGAmB,MAGe;AACf,WAAO,MAAMK,cACXpC,eAAeW,IAAD,GACdX,eAAeY,IAAD,GACdZ,eAAe+B,IAAD,CAHT;EAKR;EAmCDM,mBAME1B,MAIAC,MAKAmB,MAG8B;AAC9B,UAAMlB,eAAeb,eAAeW,IAAD;AACnC,UAAMG,eAAed,eAAeY,IAAD;AACnC,QAAIG,YAAWF,YAAD,GAAgB;AAC5B,aAAO,MAAMwB,mBACXxB,cACAC,cACAd,eAAe+B,IAAD,CAHT;IAUR;AACD,WAAO,MAAMM,mBACXxB,YADK;EAQR;EAmCDyB,sBAME3B,MAIAC,MAKAmB,MAGe;AACf,WAAO,MAAMO,sBACXtC,eAAeW,IAAD,GACdX,eAAeY,IAAD,GACdZ,eAAe+B,IAAD,CAHT;EAKR;EAEDQ,kBAAkBd,SAA6C;AAC7D,UAAMc,kBAAkBvC,eAAeyB,OAAD,CAAtC;EACD;EAEDe,iBACErB,UACAM,SACM;AACN,UAAMe,iBACJxC,eAAemB,QAAD,GACdnB,eAAeyB,OAAD,CAFhB;EAID;EAEDgB,iBACEtB,UAC2D;AAC3D,WAAO,MAAMsB,iBAAiBzC,eAAemB,QAAD,CAArC;EACR;EAEDuB,oBACEC,aACAlB,SACM;AACN,UAAMiB,oBACJ1C,eAAe2C,WAAD,GACd3C,eAAeyB,OAAD,CAFhB;EAID;EAEDmB,oBACED,aACyD;AACzD,WAAO,MAAMC,oBAAoB5C,eAAe2C,WAAD,CAAxC;EACR;AA1hBiC;;;AC/BpC,IAAME,eAAuC;EAC3CC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;AACN;AAEA,IAAMC,QAAQC,OAAOC,KAAKlZ,YAAY,EAAEmZ,KAAK,GAAG;AAChD,IAAMC,aAAa,IAAIC,OAAOL,OAAO,GAAG;AAEjC,SAASM,cAAcC,KAAa;AACzC,SAAOA,IAAIC,QAAQJ,YAAYK,WAAS;AACtC,WAAOzZ,aAAayZ,KAAK;EAC3B,CAAC;AACH;ACjWO,IAAMC,WAAW;EACtBC,sBAAsB;EACtBC,OAAO;EACPC,aAAa;EACbC,kBAAkB;EAClBC,UAAU;EACVC,SAAS;EACTC,SAAS;EACTC,UAAU;AACZ;AAWO,SAASC,SACdC,MACAC,OACAC,SACa;AAAA,MAAAC;AACbD,YAAUA,WAAW,CAAA;AAErBA,UAAQE,aAASD,qBAAGD,QAAQE,cAASD,OAAAA,qBAAIb,SAASO;AAElD,MAAI,CAACK,QAAQG,WAAW;AAEtB,UAAMC,OAAOC,gBAAgBP,MAA2BC,OAAOC,OAAO;AACtE,WAAO;;MAELM,aAAaR;MACbM;MACAG,eAAe;MACfC,mBAAmBR,QAAQE;MAC3BO,QAAQL,QAAQJ,QAAQE;;EAE5B;AAEA,QAAMQ,eAAeC,mBAAmBb,MAAME,QAAQG,SAAS;AAE/D,QAAMS,cAA2B;IAC/BN,aAAaR;IACbM,MAAMhB,SAASQ;IACfW,eAAe;IACfC,mBAAmBR,QAAQE;IAC3BO,QAAQ;;AAGV,WAASI,IAAI,GAAGA,IAAIH,aAAaI,QAAQD,KAAK;AAC5C,UAAME,YAAYL,aAAaG,CAAC;AAEhC,QAAIG,UAAUX,gBAAgBU,UAAUE,WAAWlB,OAAOC,OAAO;AAEjE,UAAM;MACJkB;MACAC;MACAjB,YAAYF,QAAQE;QAClBa,UAAUK;AAEd,QAAIJ,UAAUE,cAAcF,WAAW5B,SAASO,SAAS;AACvDqB,gBAAUE;IACZ,WAAWF,UAAUG,YAAY;AAC/BH,gBAAUG;IACZ;AAEAH,cAAUK,KAAKC,IAAIN,SAASG,UAAU;AAEtC,QAAIH,WAAWd,aAAac,UAAUJ,YAAYR,MAAM;AACtDQ,kBAAYR,OAAOY;AACnBJ,kBAAYH,SAAS;AACrBG,kBAAYL,gBAAgBM;AAC5BD,kBAAYJ,oBAAoBN;AAChCU,kBAAYN,cAAcS,UAAUE;IACtC;EACF;AAEA,SAAOL;AACT;AASA,SAASP,gBACPkB,YACAC,cACAxB,SACS;AACTuB,eAAaE,0BAA0BF,YAAYvB,OAAO;AAC1DwB,iBAAeC,0BAA0BD,cAAcxB,OAAO;AAG9D,MAAIwB,aAAaV,SAASS,WAAWT,QAAQ;AAC3C,WAAO1B,SAASQ;EAClB;AAGA,MAAI2B,eAAeC,cAAc;AAC/B,WAAOpC,SAASC;EAClB;AAGAkC,eAAaA,WAAWG,YAAW;AACnCF,iBAAeA,aAAaE,YAAW;AAGvC,MAAIH,eAAeC,cAAc;AAC/B,WAAOpC,SAASE;EAClB;AAGA,MAAIiC,WAAWI,WAAWH,YAAY,GAAG;AACvC,WAAOpC,SAASG;EAClB;AAGA,MAAIgC,WAAWK,SAAS,IAAIJ,YAAY,EAAE,GAAG;AAC3C,WAAOpC,SAASI;EAClB;AAGA,MAAI+B,WAAWK,SAASJ,YAAY,GAAG;AACrC,WAAOpC,SAASK;EAClB,WAAW+B,aAAaV,WAAW,GAAG;AAIpC,WAAO1B,SAASQ;EAClB;AAGA,MAAIiC,WAAWN,UAAU,EAAEK,SAASJ,YAAY,GAAG;AACjD,WAAOpC,SAASM;EAClB;AAIA,SAAOoC,oBAAoBP,YAAYC,YAAY;AACrD;AAQA,SAASK,WAAWE,QAAwB;AAC1C,MAAIC,UAAU;AACd,QAAMC,gBAAgBF,OAAOG,MAAM,GAAG;AACtCD,gBAAcE,QAAQC,kBAAgB;AACpC,UAAMC,qBAAqBD,aAAaF,MAAM,GAAG;AACjDG,uBAAmBF,QAAQG,uBAAqB;AAC9CN,iBAAWM,kBAAkBC,OAAO,GAAG,CAAC;IAC1C,CAAC;EACH,CAAC;AACD,SAAOP;AACT;AAYA,SAASF,oBACPP,YACAC,cACS;AACT,MAAIgB,2BAA2B;AAC/B,MAAIC,aAAa;AACjB,WAASC,sBACPC,WACAZ,QACAa,OACA;AACA,aAASC,IAAID,OAAOE,IAAIf,OAAOjB,QAAQ+B,IAAIC,GAAGD,KAAK;AACjD,YAAME,aAAahB,OAAOc,CAAC;AAC3B,UAAIE,eAAeJ,WAAW;AAC5BH,oCAA4B;AAC5B,eAAOK,IAAI;MACb;IACF;AACA,WAAO;EACT;AACA,WAASG,WAAWC,SAAgB;AAClC,UAAMC,mBAAmB,IAAID;AAC7B,UAAME,oBAAoBX,2BAA2BhB,aAAaV;AAClE,UAAMsC,UAAUhE,SAASO,UAAUwD,oBAAoBD;AACvD,WAAOE;EACT;AACA,QAAMC,aAAaX,sBAAsBlB,aAAa,CAAC,GAAGD,YAAY,CAAC;AACvE,MAAI8B,aAAa,GAAG;AAClB,WAAOjE,SAASQ;EAClB;AACA6C,eAAaY;AACb,WAASxC,IAAI,GAAGyC,IAAI9B,aAAaV,QAAQD,IAAIyC,GAAGzC,KAAK;AACnD,UAAM8B,YAAYnB,aAAaX,CAAC;AAChC4B,iBAAaC,sBAAsBC,WAAWpB,YAAYkB,UAAU;AACpE,UAAMc,QAAQd,aAAa;AAC3B,QAAI,CAACc,OAAO;AACV,aAAOnE,SAASQ;IAClB;EACF;AAEA,QAAMqD,SAASR,aAAaY;AAC5B,SAAOL,WAAWC,MAAM;AAC1B;AAkBA,SAASO,0BACPC,OAAaC,MAEL;AAAA,MADR;IAAEC;EAAuC,IAACD;AAI1CD,UAAQ,GAAGA,KAAK;AAChB,MAAI,CAACE,gBAAgB;AACnBF,YAAQG,cAAcH,KAAK;EAC7B;AACA,SAAOA;AACT;AAQA,SAASI,cACPC,MACAC,UACe;AACf,MAAIC,aAAaD;AAEjB,MAAI,OAAOA,aAAa,UAAU;AAChCC,iBAAaD,SAASA;EACxB;AAEA,QAAMN,QAAQO,WAAWF,IAAI;AAG7B,MAAIL,SAAS,MAAM;AACjB,WAAO,CAAA;EACT;AAEA,MAAIQ,MAAMC,QAAQT,KAAK,GAAG;AACxB,WAAOA;EACT;AAEA,SAAO,CAACU,OAAOV,KAAK,CAAC;AACvB;AAQA,SAASW,mBACPN,MACAO,WACA;AACA,QAAMC,YAGD,CAAA;AACL,WAASC,IAAI,GAAGC,IAAIH,UAAUI,QAAQF,IAAIC,GAAGD,KAAK;AAChD,UAAMR,WAAWM,UAAUE,CAAC;AAC5B,UAAMG,aAAaC,sBAAsBZ,QAAQ;AACjD,UAAMa,aAAaf,cAAcC,MAAMC,QAAQ;AAC/C,aAASc,IAAI,GAAGC,IAAIF,WAAWH,QAAQI,IAAIC,GAAGD,KAAK;AACjDP,gBAAUS,KAAK;QACbC,WAAWJ,WAAWC,CAAC;QACvBH;MACF,CAAC;IACH;EACF;AACA,SAAOJ;AACT;AAEA,IAAMW,uBAAuB;EAC3BC,YAAYC;EACZC,YAAY;AACd;AAMA,SAAST,sBACPZ,UACoB;AACpB,MAAI,OAAOA,aAAa,YAAY;AAClC,WAAOkB;EACT;AACA,SAAO,kCAAKA,uBAAyBlB;AACvC;;;ICnXKsB;UAAAA,aAAAA;AAAAA,EAAAA,YAAAA,YAAAA,UAAAA,IAAAA,CAAAA,IAAAA;AAAAA,EAAAA,YAAAA,YAAAA,OAAAA,IAAAA,CAAAA,IAAAA;AAAAA,EAAAA,YAAAA,YAAAA,OAAAA,IAAAA,CAAAA,IAAAA;AAAAA,EAAAA,YAAAA,YAAAA,UAAAA,IAAAA,CAAAA,IAAAA;AAAAA,EAAAA,YAAAA,YAAAA,QAAAA,IAAAA,CAAAA,IAAAA;AAAAA,GAAAA,eAAAA,aAAAA,CAAAA,EAAAA;AAQE,SAASC,cAAcC,OAA0B;AACtD,MAAIA,MAAMC,MAAMC,gBAAgB,YAAY;AAC1C,WAAOJ,WAAWK;EACnB;AACD,MAAIH,MAAMC,MAAMC,gBAAgB,UAAU;AACxC,WAAOJ,WAAWM;EACnB;AACD,MAAI,CAACJ,MAAMK,kBAAN,GAA2B;AAC9B,WAAOP,WAAWQ;EACnB;AACD,MAAIN,MAAMO,QAAN,GAAiB;AACnB,WAAOT,WAAWU;EACnB;AAED,SAAOV,WAAWW;AACnB;AAEM,SAASC,mBAAmBV,OAAsB;AACvD,QAAMW,aAAaZ,cAAcC,KAAD;AAEhC,MAAIW,eAAeb,WAAWK,UAAU;AACtC,WAAO;EACR;AACD,MAAIQ,eAAeb,WAAWM,QAAQ;AACpC,WAAO;EACR;AACD,MAAIO,eAAeb,WAAWU,OAAO;AACnC,WAAO;EACR;AACD,MAAIG,eAAeb,WAAWQ,UAAU;AACtC,WAAO;EACR;AAED,SAAO;AACR;AAEM,SAASM,iBAAiBZ,OAAsB;AACrD,QAAMW,aAAaZ,cAAcC,KAAD;AAEhC,MAAIW,eAAeb,WAAWU,OAAO;AACnC,WAAO;EACR;AAED,SAAO;AACR;AAEM,SAASK,iBAAiBb,OAAsB;AACrD,QAAMW,aAAaZ,cAAcC,KAAD;AAEhC,MAAIW,eAAeb,WAAWK,UAAU;AACtC,WAAO;EACR;AACD,MAAIQ,eAAeb,WAAWM,QAAQ;AACpC,WAAO;EACR;AACD,MAAIO,eAAeb,WAAWU,OAAO;AACnC,WAAO;EACR;AACD,MAAIG,eAAeb,WAAWQ,UAAU;AACtC,WAAO;EACR;AAED,SAAO;AACR;AAED,IAAMQ,gBAAwB,CAACC,GAAGC,MAAMD,EAAEE,UAAUC,cAAcF,EAAEC,SAA5B;AAExC,IAAME,WAAmB,CAACJ,GAAGC,MAC3BD,EAAEd,MAAMmB,gBAAgBJ,EAAEf,MAAMmB,gBAAgB,IAAI;AAEtD,IAAMC,oBAA4B,CAACN,GAAGC,MAAM;AAC1C,MAAIjB,cAAcgB,CAAD,MAAQhB,cAAciB,CAAD,GAAK;AACzC,WAAOG,SAASJ,GAAGC,CAAJ;EAChB;AAED,SAAOjB,cAAcgB,CAAD,IAAMhB,cAAciB,CAAD,IAAM,IAAI;AAClD;AAEM,IAAMM,UAAkC;EAC7C,yBAAyBD;EACzB,cAAcP;EACd,gBAAgBK;AAH6B;;;AC/E/C,IAAMI,WAAW;AACjB,IAAMC,aAAa;AAEZ,SAASC,cAAcC,KAAUC,aAA0B;AAChEC,sBACE;IACEC,IAAIN;IACJO,OAAON;IACPO,aAAa;IACbC,UAAU;IACVC,MAAM;IACNP;IACAQ,UAAU;MACRC,UAAU;QACRC,MAAM;QACNC,WAAW;QACXP,OAAO;QACPQ,SAAS,CACP;UACER,OAAO;UACPS,OAAO;QAFT,GAIA;UACET,OAAO;UACPS,OAAO;QAFT,CALO;QAUTC,cAAc;;MAEhBC,QAAQ;QACNL,MAAM;QACNN,OAAO;QACPQ,SAASI,OAAOC,KAAKC,OAAZ,EAAqBC,IAAKC,UAAS;UAC1ChB,OAAOgB;UACPP,OAAOO;QAFmC,EAAnC;QAITN,cAAcE,OAAOC,KAAKC,OAAZ,EAAqB,CAArB;MAPR;IAjBA;KA4BXG,SAAQ;AACP,UAAMC,aAAarB,YAAYsB,cAAZ;AAEnBF,QAAIG,aAAa;MACfrB,IAAIN;MACJO,OAAON;MACP2B,MAAM;MACNC,aAAa,CACX;QACED,MAAM;QACNE,SAAS;QACTC,QAASC,eAAsB;AAAA,cAAA;AAC7B,WAAA,kBAAAP,WAAWQ,IAAID,SAAf,MAAA,OAAA,SAAA,gBAA2BE,MAA3B;QACD;MALH,GAOA;QACEN,MAAM;QACNE,SAAS;QACTC,QAASC,eAAsB;AAC7B,gBAAMG,QAAQV,WAAWQ,IAAID,SAAf;AACd5B,sBAAYgC,kBAAkBD,MAAME,QAApC;QACD;MANH,GAQA;QACET,MAAM;QACNE,SAAS;QACTC,QAASC,eAAsB;AAAA,cAAA;AAC7B,WAAA,mBAAAP,WAAWQ,IAAID,SAAf,MAAA,OAAA,SAAA,iBAA2BM,MAA3B;QACD;MALH,GAOA;QACEV,MAAM;QACNE,SAAS;QACTC,QAASC,eAAsB;AAC7B,gBAAMG,QAAQV,WAAWQ,IAAID,SAAf;AACdP,qBAAWc,OAAOJ,KAAlB;QACD;OA7BQ;KAJf;AAsCAX,QAAIgB,iBAAiB;MACnBlC,IAAIN;MACJO,OAAON;MACPwC,OAAO;KAHT;AAMAhB,eAAWiB,UAAWC,WAAU;AAC9BnB,UAAIoB,kBAAkB5C,QAAtB;AACAwB,UAAIqB,mBAAmB7C,QAAvB;AAEA,YAAM8C,cAA+C,CACnD,SACA,WACA,SAHmD;AAKrD;;QAEEH,SACAG,YAAYC,SAASJ,MAAM9B,IAA3B;QACA;AACAW,YAAIwB,iBAAiB;UACnBC,SAASjD;UACT2C,OAAO;YACLO,OAAOP,MAAM9B;YACbsC,UAAUR,MAAMR,MAAMH;YACtBoB,MAAM5B,IAAI6B,IAAJ;YACNC,MAAM;cACJtB,WAAWW,MAAMR,MAAMH;eACpBW;UANA;SAFT;MAYD;KA1BH;AA6BAnB,QAAI+B,GAAGC,iBAAkBC,aAAY;AACnC,UAAIA,QAAQC,gBAAgB1D,UAAU;AACpC,cAAM2D,UAAUlC,WAAWmC,OAAX;AAChB,cAAMjD,WAAWa,IAAIqC,YAAJ;AAEjB,cAAMC,WAAWL,QAAQM,SACrBJ,QAAQI,OACLC,UAASC,SAASD,KAAKhC,WAAWyB,QAAQM,MAAzB,EAAiCG,MADrD,IAGA,CAAC,GAAGP,OAAJ;AAEJ,cAAMQ,SAASL,SAASM,KACtB,CAACC,GAAGC,MAAMjD,QAAQV,SAASO,MAAV,EAAmBmD,GAAGC,CAA7B,IAAkC3D,SAASC,QADxC;AAIf,cAAM2D,QAA+BJ,OAAO7C,IAAKa,WAAU;AACzD,gBAAMqC,aAAaC,mBAAmBtC,KAAD;AAErC,iBAAO;YACL7B,IAAI6B,MAAMH;YACVzB,OAAO4B,MAAMH;YACb0C,MAAM,CACJ;cACEnE,OAAUiE,aAAL,OAAoBrC,MAAMwC,kBAAN,IAD3B;cAEEC,WAAWC,iBAAiB1C,KAAD;cAC3B2C,iBAAiBC,iBAAiB5C,KAAD;aAJ/B;;QAQT,CAdoC;AAerCsB,gBAAQuB,YAAYT;MACrB;KA/BH;AAkCA/C,QAAI+B,GAAG0B,kBAAmBxB,aAAY;AACpC,UAAIA,QAAQC,gBAAgB1D,UAAU;AACpC,cAAMmC,QAAQV,WAAWQ,IAAIwB,QAAQyB,MAAvB;AAEd,YAAI,CAAC/C,OAAO;AACV;QACD;AAEDsB,gBAAQ0B,QAAQ;UACd,kBAAkB,CAChB;YACE5D,KAAK;YACLP,OAAOmB,MAAMH;UAFf,GAIA;YACET,KAAK;YACLP,OAAOyD,mBAAmBtC,KAAD;UAF3B,GAIA;YACEZ,KAAK;YACLP,OAAOmB,MAAMwC,kBAAN;UAFT,GAIA;YACEpD,KAAK;YACLP,OAAO,IAAIoE,KAAKjD,MAAMgD,MAAME,aAArB,EAAoCC,mBAApC;UAFT,CAbgB;UAkBlB,iBAAiB,CACf;YACE/D,KAAK;YACLP,OAAOmB,MAAMgD,MAAM7B;UAFrB,CADe;UAMjB,kBAAkB,CAChB;YACE/B,KAAK;YACLP,OAAOmB;WAHO;;MAOrB;KAxCH;EA0CD,CA5LgB;AA8LpB;;;AC9KM,IAAMoD,iBAAiB;EAC5BC,SAAS,CAACC,KAAUC,UAAiC,CAAA,MAAO;AAC1D,UAAMC,YAAYC,aAAaF,QAAQG,cAAT;AAC9B,QAAIC;AAEJ,QAAI,iBAAiBJ,WAAWA,QAAQK,aAAa;AACnDD,eAASJ,QAAQK;IAClB,OAAM;AACL,UAAIL,QAAQM,kBAAkB,OAAOC,WAAW,aAAa;AAC3D,YAAI,CAACA,OAAOC,uBAAuB;AACjC,gBAAMC,eACJ,uBAAuBT,UACnBA,QAAQU,oBACRC;AACNP,mBAAS,IAAIQ,aAAYH,YAAhB;AACTF,iBAAOC,wBAAwBJ;QAChC,OAAM;AACLA,mBAASG,OAAOC;QACjB;MACF,OAAM;AACL,cAAMC,eACJ,uBAAuBT,UAAUA,QAAQU,oBAAoBC;AAC/DP,iBAAS,IAAIQ,aAAYH,YAAhB;MACV;IACF;AAED,QAAI,CAACI,UAAU;AACbT,aAAOU,MAAP;IACD;AAED,QAAIC,mBAAmB,MAAM;;AAI7B,QAAIf,QAAQgB,iBAAiB;AAC3BZ,aAAOa,YAAYC,QAAQ;AAC3B,YAAM,CAACC,SAASC,OAAV,IAAqBpB,QAAQgB,gBAAgBZ,MAAxB;AAC3BW,yBAAmBI;AACnBC,cAAQC,KAAK,MAAM;AACjBjB,eAAOa,YAAYC,QAAQ;AAC3BlB,gBAAQsB,4BAAR,OAAA,SAAAtB,QAAQsB,yBAA2BlB,MAAnC;OAFF;IAID;AAED,QAA6CJ,QAAQM,gBAAgB;AACnEF,aACGmB,UADH,EAEGC,MAFH,6FAAA;IAKD;AAED,UAAMC,UAAU,MAAM;AACpBrB,aAAOe,QAAP;AACAJ,uBAAgB;;AAGlB,QAAIhB,IAAI2B,WAAW;AACjB3B,UAAI2B,UAAUD,OAAd;IACD,OAAM;AACL,YAAME,kBAAkB5B,IAAIoB;AAC5BpB,UAAIoB,UAAU,SAASS,kBAAkB;AACvCH,gBAAO;AACPE,wBAAe;;IAElB;AAGD,QAAIE,QAAQ;AACV9B,UAAI+B,MAAM;QACRC,eAAe;AAEb,cAAI,CAAC,KAAKC,WAAW;AACnB,kBAAMC,eAAe,CAAA;AACrBC,mBAAOC,eAAe,MAAM,aAAa;cACvCC,KAAK,MAAMH;cACXI,KAAMC,OAAMJ,OAAOK,OAAON,cAAcK,CAA5B;aAFd;UAID;AAED,eAAKN,UAAU/B,SAAf,IAA4BG;AAE5B,cAAIoC,MAAwC;AAC1C,gBAAI,SAAS,KAAKC,OAAO;AACvBC,4BAAc,MAAMtC,MAAP;YACd;UACF;QACF;OAlBH;IAoBD,OAAM;AACLL,UAAI4C,QAAQ1C,WAAWG,MAAvB;AAEA,UAAIoC,MAAwC;AAC1CE,sBAAc3C,KAAKK,MAAN;MACd;IACF;EACF;AAhG2B;;;ACoBvB,SAASwC,aAMdC,UACAC,MAGAC,OAIQ,CAAA,GACRC,OAEI,CAAA,GAC+B;AAAA,MAAA;AACnC,MAAIC,MAAwC;AAC1C,QAAI,CAACC,gBAAe,GAAI;AACtBC,cAAQC,KACN,+JADF;IAGD;EACF;AAED,QAAMC,UAAUC,SAAS,MAAMC,gBAAeT,MAAMC,MAAMC,IAAb,CAArB;AAExB,QAAMQ,eAAW,wBACfH,QAAQI,MAAMD,gBAAeE,OAAAA,wBAAAA,eAAeL,QAAQI,MAAME,cAAf;AAE7C,QAAMC,mBAAmBN,SAAS,MAAM;AACtC,UAAMO,YAAYL,YAAYM,oBAAoBT,QAAQI,KAAxC;AAClBI,cAAUE,qBAAqBP,YAAYQ,YAAYP,QACnD,gBACA;AAEJ,WAAOI;EACR,CAPgC;AASjC,QAAMI,WAAW,IAAIpB,SAASW,aAAaI,iBAAiBH,KAA3C;AACjB,QAAMS,QAAQC,SAASF,SAASG,iBAAT,CAAD;AAEtB,MAAIC,cAAc,MAAM;;AAIxBC,QACEd,YAAYQ,aACXA,iBAAgB;AAEf,QAAI,CAACA,aAAa;AAChBK,kBAAW;AACXA,oBAAcJ,SAASM,UAAWC,YAAW;AAC3CC,oBAAYP,OAAOM,MAAR;MACZ,CAFa;IAGf;EACF,GACD;IAAEE,WAAW;EAAb,CAXG;AAcL,QAAMC,UAAU,MAAM;AACpBV,aAASW,WAAWhB,iBAAiBH,KAArC;AACAgB,gBAAYP,OAAOD,SAASG,iBAAT,CAAR;;AAGbE,QAAMV,kBAAkBe,OAAnB;AAELE,iBAAe,MAAM;AACnBR,gBAAW;GADC;AAKd,QAAMS,UAAU,IAAIC,SAA8C;AAChEJ,YAAO;AACP,WAAOT,MAAMY,QAAQ,GAAGC,IAAjB;;AAGT,QAAMC,WAAW,MAAM;AACrB,WAAO,IAAIC,QACT,CAACC,SAASC,WAAW;AACnB,UAAIC,YAAY,MAAM;;AAGtB,YAAMC,MAAM,MAAM;AAChB,YAAIzB,iBAAiBH,MAAM6B,YAAY,OAAO;AAC5C,gBAAMC,mBAAmBtB,SAASuB,oBAChC5B,iBAAiBH,KADM;AAGzB,cAAI8B,iBAAiBE,SAAS;AAC5BL,sBAAS;AACTnB,qBACGyB,gBAAgB9B,iBAAiBH,KADpC,EAEGkC,KAAKT,SAASC,MAFjB;UAGD,OAAM;AACLC,sBAAS;AACTF,oBAAQK,gBAAD;UACR;QACF;;AAGHF,UAAG;AAEHD,kBAAYd,MAAMV,kBAAkByB,GAAnB;IAClB,CAzBI;EA2BR;AAGDf,QACE,MAAMJ,MAAM0B,OACXA,WAAU;AACT,QACE1B,MAAM2B,WACN,CAAC3B,MAAM4B,cACPC,iBAAiBnC,iBAAiBH,MAAMuC,kBAAkB,CACxDJ,OACA3B,SAASgC,gBAAT,CAFwD,CAA1C,GAIhB;AACA,YAAML;IACP;EACF,CAbE;AAgBL,QAAMM,SAAcC,OAAOC,SAASlC,KAAD,CAAT;AAC1B,aAAWmC,OAAOnC,OAAO;AACvB,QAAI,OAAOA,MAAMmC,GAAD,MAAgC,YAAY;AAC1DH,aAAOG,GAAD,IAAQnC,MAAMmC,GAAD;IACpB;EACF;AAEDH,SAAOlB,WAAWA;AAClBkB,SAAOpB,UAAUA;AAEjB,SAAOoB;AACR;AAEM,SAAS3C,gBAOdT,MAGAC,OAIQ,CAAA,GACRC,OAEI,CAAA,GAGJ;AACA,QAAMsD,YAAYC,MAAMzD,IAAD;AACvB,QAAM0D,YAAYD,MAAMxD,IAAD;AACvB,QAAM0D,YAAYF,MAAMvD,IAAD;AAEvB,MAAIK,UAAUiD;AAEd,MAAI,CAACI,YAAWJ,SAAD,GAAa;AAC1BjD,cAAUiD;EACX,WAAU,OAAOE,cAAc,YAAY;AAC1CnD,cAAU,iCAAKoD,YAAL;MAAgBE,UAAUL;MAAWM,SAASJ;;EACzD,OAAM;AACLnD,cAAU,iCAAKmD,YAAL;MAAgBG,UAAUL;;EACrC;AAED,QAAMO,iBAAiBC,eAAezD,OAAD;AAErC,MAAI,OAAOwD,eAAevB,YAAY,YAAY;AAChDuB,mBAAevB,UAAUuB,eAAevB,QAAf;EAC1B;AAED,SAAOuB;AAGR;;;AC9EM,SAASE,SAMdC,MAGAC,MAGAC,MAG2C;AAC3C,QAAMC,SAASC,aAAaC,eAAeL,MAAMC,MAAMC,IAA5B;AAE3B,SAAOC;AACR;;;AC3CM,SAASG,WAA4B;EAC1CC;EACAC,aAAaC;AAF6B,GAMT;AAAA,MAAA,uBAAA,wBAAA;AACjC,MAAIC,MAAwC;AAC1C,QAAI,CAACC,gBAAe,GAAI;AACtBC,cAAQC,KACN,+JADF;IAGD;EACF;AAED,QAAMC,kBAAkBC,SAAS,MAAM;AACrC,UAAMC,gBAAgBC,eAAeV,OAAD;AAElCS,kBAAwBE,IAAKC,WAAU;AACvC,UAAI,OAAOA,MAAMC,YAAY,YAAY;AACvCD,cAAMC,UAAUD,MAAMC,QAAN;MACjB;KAHF;AAMD,WAAOJ;EACR,CAV+B;AAYhC,QAAMK,kBAAiBP,wBAAAA,gBAAgBQ,MAAM,CAAtB,MAAA,OAAA,SAAA,sBAA0BD;AACjD,QAAME,sBAAqBT,yBAAAA,gBAAgBQ,MAAM,CAAtB,MAAA,OAAA,SAAA,uBAA0Bd;AAGrD,QAAMA,eACJC,OAAAA,uBAAAA,OAAAA,sBAAuBc,uBAAsBC,OAAAA,OAAAA,eAAeH,cAAD;AAC7D,MAEGA,kBAAkBE,oBACnB;AACAf,gBACGiB,UADH,EAEGC,MAFH,uMAAA;EAKD;AAED,QAAMC,mBAAmBZ,SAAS,MAChCD,gBAAgBQ,MAAMJ,IAAKU,aAAY;AACrC,UAAMC,YAAYrB,YAAYsB,oBAAoBF,OAAhC;AAClBC,cAAUE,qBAAqBvB,YAAYwB,YAAYV,QACnD,gBACA;AAEJ,WAAOO;EACR,CAPD,CAD+B;AAWjC,QAAMI,WAAW,IAAIC,gBAAgB1B,aAAamB,iBAAiBL,KAAlD;AACjB,QAAMa,QAAQC,SAASH,SAASI,iBAAT,CAAD;AAEtB,MAAIC,cAAc,MAAM;;AAIxBC,QACE/B,YAAYwB,aACXA,iBAAgB;AACf,QAAI,CAACA,aAAa;AAChBM,kBAAW;AACXA,oBAAcL,SAASO,UAAWC,YAAW;AAC3CN,cAAMO,OAAO,GAAGD,OAAOE,QAAQ,GAAGF,MAAlC;OADY;AAIdN,YAAMO,OACJ,GACAP,MAAMQ,QACN,GAAGV,SAASW,oBAAoBjB,iBAAiBL,KAA9C,CAHL;IAKD;EACF,GACD;IAAEuB,WAAW;EAAb,CAhBG;AAmBLN,QACEZ,kBACA,MAAM;AACJM,aAASa,WAAWnB,iBAAiBL,KAArC;AACAa,UAAMO,OAAO,GAAGP,MAAMQ,QAAQ,GAAGV,SAASI,iBAAT,CAAjC;EACD,GACD;IAAEU,OAAO;EAAT,CANG;AASLC,iBAAe,MAAM;AACnBV,gBAAW;EACZ,CAFa;AAId,SAAOW,SAASd,KAAD;AAChB;;;AC5JM,SAASe,iBAMdC,MAGAC,MAGAC,MAC2C;AAC3C,QAAMC,SAASC,aACbC,uBACAL,MACAC,MACAC,IAJyB;AAO3B,SAAOC;AACR;;;ACkCM,SAASG,YAMdC,MAIAC,MAGAC,MAG4D;AAAA,MAAA;AAC5D,MAAIC,MAAwC;AAC1C,QAAI,CAACC,gBAAe,GAAI;AACtBC,cAAQC,KACN,+JADF;IAGD;EACF;AAED,QAAMC,UAAUC,SAAS,MAAM;AAC7B,WAAOC,mBAAkBT,MAAMC,MAAMC,IAAb;EACzB,CAFuB;AAGxB,QAAMQ,eAAW,wBACfH,QAAQI,MAAMD,gBAAeE,OAAAA,wBAAAA,eAAeL,QAAQI,MAAME,cAAf;AAC7C,QAAMC,WAAW,IAAIC,iBACnBL,aACAA,YAAYM,uBAAuBT,QAAQI,KAA3C,CAFe;AAIjB,QAAMM,QAAQC,SAASJ,SAASK,iBAAT,CAAD;AAEtB,QAAMC,cAAcN,SAASO,UAAWC,YAAW;AACjDC,gBAAYN,OAAOK,MAAR;EACZ,CAFmB;AAIpB,QAAME,SAAS,CACbC,WACAC,kBACG;AACHZ,aAASU,OAAOC,WAAWC,aAA3B,EAA0CC,MAAM,MAAM;KAAtD;;AAKFC,QAAMrB,SAAS,MAAM;AACnBO,aAASe,WAAWnB,YAAYM,uBAAuBT,QAAQI,KAA3C,CAApB;EACD,CAFI;AAILmB,iBAAe,MAAM;AACnBV,gBAAW;EACZ,CAFa;AAId,QAAMW,aAAaC,OAAOC,SAAShB,KAAD,CAAT;AAIzBW,QACE,MAAMX,MAAMiB,OACXA,WAAU;AACT,QACEA,SACAC,iBAAiB5B,QAAQI,MAAMyB,kBAAkB,CAACF,KAAD,CAAjC,GAChB;AACA,YAAMA;IACP;EACF,CATE;AAYL,SAAO,iCACFH,aADE;IAELP;IACAa,aAAapB,MAAMO;IACnBc,OAAOrB,MAAMqB;;AAEhB;AAEM,SAAS7B,mBAMdT,MAIAC,MAGAC,MAKA;AACA,QAAMqC,YAAYC,MAAMxC,IAAD;AACvB,QAAMyC,YAAYD,MAAMvC,IAAD;AACvB,MAAIM,UAAUgC;AACd,MAAIG,cAAcH,SAAD,GAAa;AAC5B,QAAI,OAAOE,cAAc,YAAY;AACnC,YAAME,YAAYH,MAAMtC,IAAD;AACvBK,gBAAU,iCAAKoC,YAAL;QAAgBC,aAAaL;QAAWM,YAAYJ;;IAC/D,OAAM;AACLlC,gBAAU,iCAAKkC,YAAL;QAAgBG,aAAaL;;IACxC;EACF;AAED,MAAI,OAAOA,cAAc,YAAY;AACnChC,cAAU,iCAAKkC,YAAL;MAAgBI,YAAYN;;EACvC;AAED,SAAOO,eAAevC,OAAD;AAMtB;;;ACtOM,SAASwC,cACdC,MACAC,MACa;AAAA,MAAA;AACb,MAAIC,MAAwC;AAC1C,QAAI,CAACC,gBAAe,GAAI;AACtBC,cAAQC,KACN,+JADF;IAGD;EACF;AAED,QAAMC,UAAUC,SAAS,MAAMC,iBAAgBR,MAAMC,IAAP,CAAtB;AACxB,QAAMQ,eAAW,wBACfH,QAAQI,MAAMD,gBAAeE,OAAAA,wBAAAA,eAAeL,QAAQI,MAAME,cAAf;AAE7C,QAAMC,aAAaC,IAAG;AAEtB,QAAMC,WAAW,MAAM;AACrBF,eAAWH,QAAQD,YAAYI,WAAWP,OAAvB;;AAGrB,QAAMU,cAAcP,YAAYQ,cAAZ,EAA4BC,UAAUH,QAAtC;AAEpBI,cAAYJ,QAAD;AAEXK,iBAAe,MAAM;AACnBJ,gBAAW;EACZ,CAFa;AAId,SAAOH;AACR;AAEM,SAASL,iBACdR,MACAC,OAAqB,CAAA,GACrB;AACA,QAAMoB,YAAYC,MAAMtB,IAAD;AACvB,QAAMuB,YAAYD,MAAMrB,IAAD;AAEvB,MAAIuB,UAAUH;AAEd,MAAII,YAAWJ,SAAD,GAAa;AACzBG,cAAU,iCAAKD,YAAL;MAAgBG,UAAUL;;EACrC,OAAM;AAELG,cAAUH,aAAa,CAAA;EACxB;AAED,SAAOM,eAAeH,OAAD;AACtB;;;AClDM,SAASI,cACdC,MACAC,MACa;AAAA,MAAA;AACb,MAAIC,MAAwC;AAC1C,QAAI,CAACC,gBAAe,GAAI;AACtBC,cAAQC,KACN,+JADF;IAGD;EACF;AAED,QAAMC,UAAUC,SAAS,MAAMC,iBAAgBR,MAAMC,IAAP,CAAtB;AACxB,QAAMQ,eAAW,wBACfH,QAAQI,MAAMD,gBAAeE,OAAAA,wBAAAA,eAAeL,QAAQI,MAAME,cAAf;AAE7C,QAAMC,aAAaC,IAAG;AAEtB,QAAMC,WAAW,MAAM;AACrBF,eAAWH,QAAQD,YAAYI,WAAWP,OAAvB;;AAGrB,QAAMU,cAAcP,YAAYQ,iBAAZ,EAA+BC,UAAUH,QAAzC;AAEpBI,cAAYJ,QAAD;AAEXK,iBAAe,MAAM;AACnBJ,gBAAW;EACZ,CAFa;AAId,SAAOH;AACR;AAEM,SAASL,iBACdR,MACAC,OAAwB,CAAA,GACxB;AACA,QAAMoB,YAAYC,MAAMtB,IAAD;AACvB,QAAMuB,YAAYD,MAAMrB,IAAD;AAEvB,MAAIuB,UAAUH;AAEd,MAAII,YAAWJ,SAAD,GAAa;AACzBG,cAAU,iCAAKD,YAAL;MAAgBG,aAAaL;;EACxC,OAAM;AAELG,cAAUH,aAAa,CAAA;EACxB;AAED,SAAOM,eAAeH,OAAD;AACtB;", "names": ["Subscribable", "constructor", "listeners", "Set", "subscribe", "bind", "listener", "identity", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size", "isServer", "window", "noop", "undefined", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "Infinity", "difference", "array1", "array2", "filter", "x", "includes", "replaceAt", "array", "index", "copy", "slice", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "parseMutationArgs", "<PERSON><PERSON><PERSON>", "mutationFn", "parseFilter<PERSON><PERSON>s", "parseMutationFilterArgs", "matchQuery", "filters", "query", "type", "exact", "fetchStatus", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "fetching", "hashQuery<PERSON>ey", "status", "hashFn", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "partialDeepEqual", "some", "replaceEqualDeep", "is<PERSON><PERSON>A<PERSON>y", "aSize", "length", "bItems", "bSize", "equalItems", "i", "shallowEqualObjects", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "constructor", "prot", "prototype", "hasOwnProperty", "toString", "call", "isError", "Error", "sleep", "timeout", "Promise", "resolve", "setTimeout", "scheduleMicrotask", "callback", "then", "getAbortController", "AbortController", "replaceData", "prevData", "data", "isDataEqual", "structuralSharing", "FocusManager", "Subscribable", "constructor", "setup", "onFocus", "isServer", "window", "addEventListener", "listener", "removeEventListener", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "undefined", "focused", "setFocused", "changed", "listeners", "for<PERSON>ach", "isFocused", "document", "includes", "visibilityState", "focusManager", "onlineEvents", "OnlineManager", "Subscribable", "constructor", "setup", "onOnline", "isServer", "window", "addEventListener", "listener", "for<PERSON>ach", "event", "removeEventListener", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "undefined", "online", "setOnline", "changed", "listeners", "isOnline", "navigator", "onLine", "onlineManager", "defaultRetryDelay", "failureCount", "Math", "min", "canFetch", "networkMode", "onlineManager", "isOnline", "CancelledError", "constructor", "options", "revert", "silent", "isCancelledError", "value", "createRetryer", "config", "isRetryCancelled", "isResolved", "continueFn", "promiseResolve", "promiseReject", "promise", "Promise", "outerResolve", "outerReject", "cancel", "cancelOptions", "reject", "abort", "cancelRetry", "continueRetry", "shouldP<PERSON>e", "focusManager", "isFocused", "resolve", "onSuccess", "onError", "pause", "continueResolve", "canContinue", "onPause", "then", "undefined", "onContinue", "run", "promiseOrValue", "fn", "error", "catch", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "sleep", "continue", "didContinue", "defaultLogger", "console", "createNotifyManager", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "batch", "result", "flush", "schedule", "push", "scheduleMicrotask", "batchCalls", "args", "originalQueue", "length", "for<PERSON>ach", "setNotifyFunction", "fn", "setBatchNotifyFunction", "notify<PERSON><PERSON>ger", "Removable", "destroy", "clearGcTimeout", "scheduleGc", "isValidTimeout", "cacheTime", "gcTimeout", "setTimeout", "optionalRemove", "updateCacheTime", "newCacheTime", "Math", "max", "isServer", "Infinity", "clearTimeout", "undefined", "Query", "Removable", "constructor", "config", "abortSignalConsumed", "defaultOptions", "setOptions", "options", "observers", "cache", "logger", "defaultLogger", "query<PERSON><PERSON>", "queryHash", "initialState", "state", "getDefaultState", "scheduleGc", "meta", "updateCacheTime", "cacheTime", "optionalRemove", "length", "fetchStatus", "remove", "setData", "newData", "data", "replaceData", "dispatch", "type", "dataUpdatedAt", "updatedAt", "manual", "setState", "setStateOptions", "cancel", "promise", "retryer", "then", "noop", "catch", "Promise", "resolve", "destroy", "silent", "reset", "isActive", "some", "observer", "enabled", "isDisabled", "getObserversCount", "isStale", "isInvalidated", "getCurrentResult", "isStaleByTime", "staleTime", "timeUntilStale", "onFocus", "find", "x", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "continue", "onOnline", "shouldFetchOnReconnect", "addObserver", "includes", "push", "clearGcTimeout", "notify", "query", "removeObserver", "filter", "revert", "cancelRetry", "invalidate", "fetch", "fetchOptions", "continueRetry", "queryFn", "process", "Array", "isArray", "error", "abortController", "getAbortController", "queryFnContext", "pageParam", "undefined", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "reject", "context", "behavior", "onFetch", "revertState", "fetchMeta", "onError", "isCancelledError", "onSettled", "isFetchingOptimistic", "createRetryer", "fn", "abort", "bind", "onSuccess", "Error", "onFail", "failureCount", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "action", "reducer", "fetchFailureCount", "fetchFailureReason", "canFetch", "status", "dataUpdateCount", "Date", "now", "errorUpdateCount", "errorUpdatedAt", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onQueryUpdate", "initialData", "hasData", "initialDataUpdatedAt", "Query<PERSON>ache", "Subscribable", "constructor", "config", "queries", "queriesMap", "build", "client", "options", "state", "query<PERSON><PERSON>", "queryHash", "hashQueryKeyByOptions", "query", "get", "Query", "cache", "logger", "<PERSON><PERSON><PERSON><PERSON>", "defaultQueryOptions", "defaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "push", "notify", "type", "remove", "queryInMap", "destroy", "filter", "x", "clear", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "getAll", "find", "arg1", "arg2", "filters", "parseFilter<PERSON><PERSON>s", "exact", "matchQuery", "findAll", "Object", "keys", "length", "event", "listeners", "listener", "onFocus", "onOnline", "Mutation", "Removable", "constructor", "config", "defaultOptions", "mutationId", "mutationCache", "logger", "defaultLogger", "observers", "state", "getDefaultState", "setOptions", "options", "scheduleGc", "updateCacheTime", "cacheTime", "meta", "setState", "dispatch", "type", "addObserver", "observer", "includes", "push", "clearGcTimeout", "notify", "mutation", "removeObserver", "filter", "x", "optionalRemove", "length", "status", "remove", "continue", "retryer", "execute", "executeMutation", "createRetryer", "fn", "mutationFn", "Promise", "reject", "variables", "onFail", "failureCount", "error", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "promise", "restored", "onMutate", "context", "data", "onSuccess", "onSettled", "onError", "process", "undefined", "action", "reducer", "failureReason", "isPaused", "canFetch", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onMutationUpdate", "MutationCache", "Subscribable", "constructor", "config", "mutations", "mutationId", "build", "client", "options", "state", "mutation", "Mutation", "mutationCache", "logger", "<PERSON><PERSON><PERSON><PERSON>", "defaultMutationOptions", "defaultOptions", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "undefined", "add", "push", "notify", "type", "remove", "filter", "x", "clear", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "getAll", "find", "filters", "exact", "matchMutation", "findAll", "event", "listeners", "listener", "resumePausedMutations", "resuming", "Promise", "resolve", "then", "pausedMutations", "isPaused", "reduce", "promise", "continue", "catch", "noop", "infiniteQueryBehavior", "onFetch", "context", "fetchFn", "refetchPage", "fetchOptions", "meta", "fetchMore", "pageParam", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "state", "data", "pages", "oldPageParams", "pageParams", "newPageParams", "cancelled", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "aborted", "addEventListener", "queryFn", "options", "Promise", "reject", "queryHash", "buildNewPages", "param", "page", "previous", "fetchPage", "manual", "length", "resolve", "queryFnContext", "query<PERSON><PERSON>", "queryFnResult", "promise", "then", "getNextPageParam", "getPreviousPageParam", "shouldFetchFirstPage", "i", "shouldFetchNextPage", "finalPromise", "hasNextPage", "Array", "isArray", "nextPageParam", "hasPreviousPage", "previousPageParam", "QueryClient", "constructor", "config", "queryCache", "Query<PERSON>ache", "mutationCache", "MutationCache", "logger", "defaultLogger", "defaultOptions", "queryDefaults", "mutationDefaults", "mountCount", "error", "mount", "unsubscribeFocus", "focusManager", "subscribe", "isFocused", "resumePausedMutations", "onFocus", "unsubscribeOnline", "onlineManager", "isOnline", "onOnline", "unmount", "undefined", "isFetching", "arg1", "arg2", "filters", "parseFilter<PERSON><PERSON>s", "fetchStatus", "findAll", "length", "isMutating", "fetching", "getQueryData", "query<PERSON><PERSON>", "find", "state", "data", "ensureQueryData", "arg3", "parsedOptions", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cachedData", "Promise", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "getQueriesData", "query<PERSON>eyOrFilters", "get<PERSON><PERSON><PERSON><PERSON>ache", "map", "setQueryData", "updater", "options", "query", "prevData", "functionalUpdate", "defaultedOptions", "defaultQueryOptions", "build", "setData", "manual", "setQueriesData", "notify<PERSON><PERSON>ger", "batch", "getQueryState", "removeQueries", "for<PERSON>ach", "remove", "resetQueries", "refetchFilters", "type", "reset", "refetchQueries", "cancelQueries", "cancelOptions", "revert", "promises", "cancel", "all", "then", "noop", "catch", "invalidateQueries", "invalidate", "refetchType", "filter", "isDisabled", "fetch", "cancelRefetch", "meta", "refetchPage", "promise", "throwOnError", "retry", "isStaleByTime", "staleTime", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "behavior", "infiniteQueryBehavior", "prefetchInfiniteQuery", "getMutationCache", "<PERSON><PERSON><PERSON><PERSON>", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "x", "hashQuery<PERSON>ey", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstMatchingDefaults", "partialMatchKey", "process", "matchingDefaults", "JSON", "stringify", "setMutationDefaults", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "_defaulted", "queries", "queryHash", "hashQueryKeyByOptions", "refetchOnReconnect", "networkMode", "useErrorBoundary", "suspense", "defaultMutationOptions", "mutations", "clear", "QueryObserver", "Subscribable", "constructor", "client", "options", "trackedProps", "Set", "selectError", "bindMethods", "setOptions", "remove", "bind", "refetch", "onSubscribe", "listeners", "size", "<PERSON><PERSON><PERSON><PERSON>", "addObserver", "shouldFetchOnMount", "executeFetch", "updateTimers", "onUnsubscribe", "hasListeners", "destroy", "shouldFetchOnReconnect", "shouldFetchOn", "refetchOnReconnect", "shouldFetchOnWindowFocus", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "removeObserver", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "defaultQueryOptions", "isDataEqual", "<PERSON><PERSON><PERSON><PERSON>", "error", "shallowEqualObjects", "get<PERSON><PERSON><PERSON><PERSON>ache", "notify", "type", "query", "observer", "enabled", "Error", "query<PERSON><PERSON>", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "staleTime", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "build", "result", "createResult", "shouldAssignObserverCurrentProperties", "currentResult", "currentResultOptions", "currentResultState", "state", "getCurrentResult", "trackResult", "trackedResult", "Object", "keys", "for<PERSON>ach", "key", "defineProperty", "configurable", "enumerable", "get", "add", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refetchPage", "fetch", "meta", "fetchOptimistic", "defaultedOptions", "isFetchingOptimistic", "then", "fetchOptions", "cancelRefetch", "promise", "throwOnError", "catch", "noop", "isServer", "isStale", "isValidTimeout", "time", "timeUntilStale", "dataUpdatedAt", "timeout", "staleTimeoutId", "setTimeout", "refetchInterval", "data", "nextInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "focusManager", "isFocused", "clearTimeout", "undefined", "clearInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "errorUpdatedAt", "fetchStatus", "status", "isPreviousData", "isPlaceholderData", "_optimisticResults", "fetchOnMount", "fetchOptionally", "canFetch", "networkMode", "keepPreviousData", "isSuccess", "select", "selectFn", "selectResult", "replaceData", "process", "placeholderData", "Date", "now", "isFetching", "isLoading", "isError", "isInitialLoading", "failureCount", "fetchFailureCount", "failureReason", "fetchFailureReason", "errorUpdateCount", "isFetched", "dataUpdateCount", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isPaused", "isRefetchError", "nextResult", "defaultNotifyOptions", "cache", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "useErrorBoundary", "some", "<PERSON><PERSON><PERSON>", "changed", "has", "onQueryUpdate", "action", "onSuccess", "manual", "isCancelledError", "onError", "notify<PERSON><PERSON>ger", "batch", "onSettled", "listener", "shouldLoadOnMount", "retryOnMount", "refetchOnMount", "field", "value", "suspense", "isStaleByTime", "optimisticResult", "QueriesObserver", "Subscribable", "constructor", "client", "queries", "result", "observers", "observersMap", "setQueries", "onSubscribe", "listeners", "size", "for<PERSON>ach", "observer", "subscribe", "onUpdate", "onUnsubscribe", "destroy", "Set", "notifyOptions", "notify<PERSON><PERSON>ger", "batch", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "setOptions", "defaultedQueryOptions", "newObservers", "map", "newObserversMap", "Object", "fromEntries", "options", "queryHash", "newResult", "getCurrentResult", "hasIndexChange", "some", "index", "length", "hasListeners", "difference", "notify", "getQueries", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getObservers", "getOptimisticResult", "prevObserversMap", "Map", "defaultQueryOptions", "matchingObservers", "flatMap", "defaultedOptions", "get", "matchedQueryHashes", "unmatchedQueries", "filter", "has", "matchingObserversSet", "unmatchedObservers", "prevObserver", "getObserver", "currentObserver", "QueryObserver", "newOrReusedObservers", "keepPreviousData", "previouslyUsedObserver", "undefined", "sortMatchesByOrderOfQueries", "a", "b", "indexOf", "concat", "sort", "replaceAt", "listener", "InfiniteQueryObserver", "QueryObserver", "constructor", "client", "options", "bindMethods", "fetchNextPage", "bind", "fetchPreviousPage", "setOptions", "notifyOptions", "behavior", "infiniteQueryBehavior", "getOptimisticResult", "pageParam", "fetch", "meta", "fetchMore", "direction", "createResult", "query", "state", "result", "isFetching", "isRefetching", "isFetchingNextPage", "fetchMeta", "isFetchingPreviousPage", "hasNextPage", "data", "pages", "hasPreviousPage", "MutationObserver", "Subscribable", "constructor", "client", "options", "setOptions", "bindMethods", "updateResult", "mutate", "bind", "reset", "prevOptions", "defaultMutationOptions", "shallowEqualObjects", "getMutationCache", "notify", "type", "mutation", "currentMutation", "observer", "onUnsubscribe", "hasListeners", "removeObserver", "onMutationUpdate", "action", "notifyOptions", "listeners", "onSuccess", "onError", "getCurrentResult", "currentResult", "undefined", "variables", "mutateOptions", "build", "addObserver", "execute", "state", "getDefaultState", "isLoading", "status", "result", "isPending", "isSuccess", "isError", "isIdle", "notify<PERSON><PERSON>ger", "batch", "data", "context", "onSettled", "error", "for<PERSON>ach", "listener", "dehydrateMutation", "mutation", "<PERSON><PERSON><PERSON>", "options", "state", "dehydrate<PERSON><PERSON>y", "query", "query<PERSON><PERSON>", "queryHash", "defaultShouldDehydrateMutation", "isPaused", "defaultShouldDehydrateQuery", "status", "dehydrate", "client", "mutations", "queries", "dehydrateMutations", "shouldDehydrateMutation", "getMutationCache", "getAll", "for<PERSON>ach", "push", "dehydrateQueries", "shouldDehydrateQuery", "get<PERSON><PERSON><PERSON><PERSON>ache", "hydrate", "dehydratedState", "mutationCache", "queryCache", "dehydratedMutation", "build", "defaultOptions", "get", "dataUpdatedAt", "fetchStatus", "_ignored", "dehydratedQueryState", "setState", "VUE_QUERY_CLIENT", "getClientKey", "key", "suffix", "is<PERSON>uery<PERSON>ey", "value", "Array", "isArray", "isMutationKey", "updateState", "state", "update", "Object", "keys", "for<PERSON>ach", "cloneDeep", "customizer", "result", "undefined", "isRef", "map", "val", "isPlainObject", "entries", "fromEntries", "cloneDeepUnref", "obj", "unref", "prototype", "toString", "call", "getPrototypeOf", "shouldThrowError", "_useErrorBoundary", "params", "useQueryClient", "id", "key", "getClientKey", "queryClient", "inject", "vm", "getCurrentInstance", "proxy", "Error", "Query<PERSON>ache", "QC", "find", "arg1", "arg2", "arg1Unreffed", "cloneDeepUnref", "arg2Unreffed", "is<PERSON>uery<PERSON>ey", "findAll", "MutationCache", "MC", "find", "filters", "cloneDeepUnref", "findAll", "QueryClient", "QC", "constructor", "config", "unreffedConfig", "cloneDeepUnref", "vueQueryConfig", "logger", "defaultOptions", "queryCache", "Query<PERSON>ache", "mutationCache", "MutationCache", "isRestoring", "ref", "isFetching", "arg1", "arg2", "arg1Unreffed", "arg2Unreffed", "is<PERSON>uery<PERSON>ey", "isMutating", "filters", "getQueryData", "query<PERSON><PERSON>", "getQueriesData", "query<PERSON>eyOrFilters", "unreffed", "setQueryData", "updater", "options", "setQueriesData", "arg3Unreffed", "getQueryState", "removeQueries", "resetQueries", "arg3", "cancelQueries", "invalidateQueries", "refetchQueries", "<PERSON><PERSON><PERSON><PERSON>", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "prefetchInfiniteQuery", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setMutationDefaults", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "characterMap", "À", "Á", "Â", "Ã", "Ä", "Å", "Ấ", "Ắ", "Ẳ", "Ẵ", "Ặ", "<PERSON>", "Ầ", "Ằ", "Ȃ", "Ç", "Ḉ", "È", "É", "Ê", "Ë", "Ế", "Ḗ", "Ề", "Ḕ", "Ḝ", "Ȇ", "Ì", "Í", "Î", "Ï", "Ḯ", "Ȋ", "Ð", "Ñ", "Ò", "<PERSON>", "Ô", "Õ", "Ö", "Ø", "Ố", "Ṍ", "Ṓ", "Ȏ", "Ù", "Ú", "Û", "Ü", "Ý", "à", "á", "â", "ã", "ä", "å", "ấ", "ắ", "ẳ", "ẵ", "ặ", "æ", "ầ", "ằ", "ȃ", "ç", "ḉ", "è", "é", "ê", "ë", "ế", "ḗ", "ề", "ḕ", "ḝ", "ȇ", "ì", "í", "î", "ï", "ḯ", "ȋ", "ð", "ñ", "ò", "ó", "ô", "õ", "ö", "ø", "ố", "ṍ", "ṓ", "ȏ", "ù", "ú", "û", "ü", "ý", "ÿ", "Ā", "ā", "Ă", "ă", "Ą", "ą", "Ć", "ć", "Ĉ", "ĉ", "Ċ", "ċ", "Č", "č", "C̆", "c̆", "Ď", "ď", "Đ", "đ", "Ē", "ē", "Ĕ", "ĕ", "Ė", "ė", "Ę", "ę", "Ě", "ě", "Ĝ", "Ǵ", "ĝ", "ǵ", "Ğ", "ğ", "Ġ", "ġ", "Ģ", "ģ", "Ĥ", "ĥ", "Ħ", "ħ", "Ḫ", "ḫ", "Ĩ", "ĩ", "Ī", "ī", "Ĭ", "ĭ", "Į", "į", "İ", "ı", "Ĳ", "ĳ", "Ĵ", "ĵ", "Ķ", "ķ", "Ḱ", "ḱ", "K̆", "k̆", "Ĺ", "ĺ", "Ļ", "ļ", "Ľ", "ľ", "Ŀ", "ŀ", "Ł", "ł", "Ḿ", "ḿ", "M̆", "m̆", "Ń", "ń", "Ņ", "ņ", "Ň", "ň", "ŉ", "N̆", "n̆", "Ō", "<PERSON>", "Ŏ", "ŏ", "Ő", "ő", "Œ", "œ", "P̆", "p̆", "Ŕ", "ŕ", "Ŗ", "ŗ", "Ř", "ř", "R̆", "r̆", "Ȓ", "ȓ", "Ś", "ś", "Ŝ", "ŝ", "Ş", "Ș", "ș", "ş", "Š", "š", "Ţ", "ţ", "ț", "Ț", "Ť", "ť", "Ŧ", "ŧ", "T̆", "t̆", "Ũ", "ũ", "Ū", "ū", "Ŭ", "ŭ", "Ů", "ů", "Ű", "ű", "Ų", "ų", "Ȗ", "ȗ", "V̆", "v̆", "Ŵ", "ŵ", "Ẃ", "ẃ", "X̆", "x̆", "Ŷ", "ŷ", "Ÿ", "Y̆", "y̆", "Ź", "ź", "Ż", "ż", "Ž", "ž", "ſ", "ƒ", "Ơ", "ơ", "Ư", "ư", "Ǎ", "ǎ", "Ǐ", "ǐ", "Ǒ", "ǒ", "Ǔ", "ǔ", "Ǖ", "ǖ", "Ǘ", "ǘ", "Ǚ", "ǚ", "Ǜ", "ǜ", "Ứ", "ứ", "Ṹ", "ṹ", "Ǻ", "ǻ", "Ǽ", "ǽ", "Ǿ", "ǿ", "Þ", "þ", "Ṕ", "ṕ", "Ṥ", "ṥ", "X́", "x́", "Ѓ", "ѓ", "Ќ", "ќ", "A̋", "a̋", "E̋", "e̋", "I̋", "i̋", "Ǹ", "ǹ", "Ồ", "ồ", "Ṑ", "ṑ", "Ừ", "ừ", "Ẁ", "ẁ", "Ỳ", "ỳ", "Ȁ", "ȁ", "Ȅ", "ȅ", "Ȉ", "ȉ", "Ȍ", "ȍ", "Ȑ", "ȑ", "Ȕ", "ȕ", "B̌", "b̌", "Č̣", "č̣", "Ê̌", "ê̌", "F̌", "f̌", "Ǧ", "ǧ", "Ȟ", "ȟ", "J̌", "ǰ", "Ǩ", "ǩ", "M̌", "m̌", "P̌", "p̌", "Q̌", "q̌", "Ř̩", "ř̩", "Ṧ", "ṧ", "V̌", "v̌", "W̌", "w̌", "X̌", "x̌", "Y̌", "y̌", "A̧", "a̧", "B̧", "b̧", "Ḑ", "ḑ", "Ȩ", "ȩ", "Ɛ̧", "ɛ̧", "Ḩ", "ḩ", "I̧", "i̧", "Ɨ̧", "ɨ̧", "M̧", "m̧", "O̧", "o̧", "Q̧", "q̧", "U̧", "u̧", "X̧", "x̧", "Z̧", "z̧", "chars", "Object", "keys", "join", "allAccents", "RegExp", "removeAccents", "str", "replace", "match", "rankings", "CASE_SENSITIVE_EQUAL", "EQUAL", "STARTS_WITH", "WORD_STARTS_WITH", "CONTAINS", "ACRONYM", "MATCHES", "NO_MATCH", "rankItem", "item", "value", "options", "_options$threshold", "threshold", "accessors", "rank", "getMatchRanking", "rankedValue", "accessorIndex", "accessorThreshold", "passed", "valuesToRank", "getAllValuesToRank", "rankingInfo", "i", "length", "rankValue", "newRank", "itemValue", "minRanking", "maxRanking", "attributes", "Math", "min", "testString", "stringToRank", "prepareValueForComparison", "toLowerCase", "startsWith", "includes", "getAcronym", "getClosenessRanking", "string", "acronym", "wordsInString", "split", "for<PERSON>ach", "wordInString", "splitByHyphenWords", "splitByHyphenWord", "substr", "matchingInOrderCharCount", "char<PERSON><PERSON>ber", "findMatchingCharacter", "matchChar", "index", "j", "J", "stringChar", "getRanking", "spread", "spreadPercentage", "inOrderPercentage", "ranking", "firstIndex", "I", "found", "prepareValueForComparison", "value", "_ref", "keepDiacritics", "removeAccents", "getItemValues", "item", "accessor", "accessorFn", "Array", "isArray", "String", "getAllValuesToRank", "accessors", "allValues", "j", "J", "length", "attributes", "getAccessorAttributes", "itemValues", "i", "I", "push", "itemValue", "defaultKeyAttributes", "maxRanking", "Infinity", "minRanking", "QueryState", "getQueryState", "query", "state", "fetchStatus", "Fetching", "Paused", "getObserversCount", "Inactive", "isStale", "Stale", "Fresh", "getQueryStateLabel", "queryState", "getQueryStatusFg", "getQueryStatusBg", "queryHashSort", "a", "b", "queryHash", "localeCompare", "dateSort", "dataUpdatedAt", "statusAndDateSort", "sortFns", "pluginId", "pluginName", "setupDevtools", "app", "queryClient", "setupDevtoolsPlugin", "id", "label", "packageName", "homepage", "logo", "settings", "baseSort", "type", "component", "options", "value", "defaultValue", "sortFn", "Object", "keys", "sortFns", "map", "key", "api", "queryCache", "get<PERSON><PERSON><PERSON><PERSON>ache", "addInspector", "icon", "nodeActions", "tooltip", "action", "queryHash", "get", "fetch", "query", "invalidateQueries", "query<PERSON><PERSON>", "reset", "remove", "addTimelineLayer", "color", "subscribe", "event", "sendInspectorTree", "sendInspectorState", "queryEvents", "includes", "addTimelineEvent", "layerId", "title", "subtitle", "time", "now", "data", "on", "getInspectorTree", "payload", "inspectorId", "queries", "getAll", "getSettings", "filtered", "filter", "item", "rankItem", "passed", "sorted", "sort", "a", "b", "nodes", "stateLabel", "getQueryStateLabel", "tags", "getObserversCount", "textColor", "getQueryStatusFg", "backgroundColor", "getQueryStatusBg", "rootNodes", "getInspectorState", "nodeId", "state", "Date", "dataUpdatedAt", "toLocaleTimeString", "<PERSON><PERSON><PERSON>ueryP<PERSON>in", "install", "app", "options", "client<PERSON>ey", "getClientKey", "queryClient<PERSON>ey", "client", "queryClient", "contextSharing", "window", "__VUE_QUERY_CONTEXT__", "clientConfig", "queryClientConfig", "undefined", "QueryClient", "isServer", "mount", "persister<PERSON>n<PERSON>", "clientPersister", "isRestoring", "value", "unmount", "promise", "then", "clientPersisterOnSuccess", "<PERSON><PERSON><PERSON><PERSON>", "error", "cleanup", "onUnmount", "originalUnmount", "vueQueryUnmount", "isVue2", "mixin", "beforeCreate", "_provided", "provideCache", "Object", "defineProperty", "get", "set", "v", "assign", "process", "$root", "setupDevtools", "provide", "useBaseQuery", "Observer", "arg1", "arg2", "arg3", "process", "getCurrentScope", "console", "warn", "options", "computed", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queryClient", "value", "useQueryClient", "queryClient<PERSON>ey", "defaultedOptions", "defaulted", "defaultQueryOptions", "_optimisticResults", "isRestoring", "observer", "state", "reactive", "getCurrentResult", "unsubscribe", "watch", "subscribe", "result", "updateState", "immediate", "updater", "setOptions", "onScopeDispose", "refetch", "args", "suspense", "Promise", "resolve", "reject", "stopWatch", "run", "enabled", "optimisticResult", "getOptimisticResult", "isStale", "fetchOptimistic", "then", "error", "isError", "isFetching", "shouldThrowError", "useErrorBoundary", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "object", "toRefs", "readonly", "key", "plainArg1", "unref", "plainArg2", "plainArg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "clondedOptions", "cloneDeepUnref", "useQuery", "arg1", "arg2", "arg3", "result", "useBaseQuery", "QueryObserver", "useQueries", "queries", "queryClient", "queryClientInjected", "process", "getCurrentScope", "console", "warn", "unreffedQueries", "computed", "clonedQueries", "cloneDeepUnref", "map", "query", "enabled", "queryClient<PERSON>ey", "value", "optionsQueryClient", "useQueryClient", "<PERSON><PERSON><PERSON><PERSON>", "error", "defaultedQueries", "options", "defaulted", "defaultQueryOptions", "_optimisticResults", "isRestoring", "observer", "QueriesObserver", "state", "reactive", "getCurrentResult", "unsubscribe", "watch", "subscribe", "result", "splice", "length", "getOptimisticResult", "immediate", "setQueries", "flush", "onScopeDispose", "readonly", "useInfiniteQuery", "arg1", "arg2", "arg3", "result", "useBaseQuery", "InfiniteQueryObserver", "useMutation", "arg1", "arg2", "arg3", "process", "getCurrentScope", "console", "warn", "options", "computed", "parseMutationArgs", "queryClient", "value", "useQueryClient", "queryClient<PERSON>ey", "observer", "MutationObserver", "defaultMutationOptions", "state", "reactive", "getCurrentResult", "unsubscribe", "subscribe", "result", "updateState", "mutate", "variables", "mutateOptions", "catch", "watch", "setOptions", "onScopeDispose", "resultRefs", "toRefs", "readonly", "error", "shouldThrowError", "useErrorBoundary", "mutateAsync", "reset", "plainArg1", "unref", "plainArg2", "isMutationKey", "plainArg3", "<PERSON><PERSON><PERSON>", "mutationFn", "cloneDeepUnref", "useIsFetching", "arg1", "arg2", "process", "getCurrentScope", "console", "warn", "filters", "computed", "parseFilter<PERSON><PERSON>s", "queryClient", "value", "useQueryClient", "queryClient<PERSON>ey", "isFetching", "ref", "listener", "unsubscribe", "get<PERSON><PERSON><PERSON><PERSON>ache", "subscribe", "watchEffect", "onScopeDispose", "plainArg1", "unref", "plainArg2", "options", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "cloneDeepUnref", "useIsMutating", "arg1", "arg2", "process", "getCurrentScope", "console", "warn", "filters", "computed", "parseFilter<PERSON><PERSON>s", "queryClient", "value", "useQueryClient", "queryClient<PERSON>ey", "isMutating", "ref", "listener", "unsubscribe", "getMutationCache", "subscribe", "watchEffect", "onScopeDispose", "plainArg1", "unref", "plainArg2", "options", "is<PERSON>uery<PERSON>ey", "<PERSON><PERSON><PERSON>", "cloneDeepUnref"]}