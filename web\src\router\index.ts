import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: 'Reforged',
        metaTags: [
          {
            name: 'description',
            content:
              'An AQW private server that is dedicated to providing a fun and immersive experience for players.',
          },
          {
            property: 'og:title',
            content:
              'An AQW private server that is dedicated to providing a fun and immersive experience for players.',
          },
          {
            property: 'og:description',
            content:
              'An AQW private server that is dedicated to providing a fun and immersive experience for players.',
          },
        ],
      },
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../features/login/views/Login.vue'),
      beforeEnter: (_to, _from, next) => {
        const authStore = useAuthStore()
        if (authStore.getIsAuthenticated()) {
          next('/')
        } else {
          next()
        }
      },
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../features/register/views/Register.vue'),
      beforeEnter: (_to, _from, next) => {
        const authStore = useAuthStore()
        if (authStore.getIsAuthenticated()) {
          next('/')
        } else {
          next()
        }
      },
    },
  ],
})

export default router
