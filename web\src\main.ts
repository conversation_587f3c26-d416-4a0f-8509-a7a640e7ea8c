import './assets/main.css'
import 'virtual:uno.css'
import '@unocss/reset/tailwind.css'
import 'core-js/features/array/includes'
import 'core-js/features/promise'

// fontsource
import '@fontsource/new-rocker'
import '@fontsource/rocknroll-one'
import '@fontsource/poppins/100.css'
import '@fontsource/poppins/200.css'
import '@fontsource/poppins/300.css'
import '@fontsource/poppins/400.css'
import '@fontsource/poppins/500.css'
import '@fontsource/poppins/600.css'
import '@fontsource/poppins/700.css'
import '@fontsource/poppins/800.css'
import '@fontsource/poppins/900.css'
import '@fontsource/poppins/100-italic.css'
import '@fontsource/poppins/200-italic.css'
import '@fontsource/poppins/300-italic.css'
import '@fontsource/poppins/400-italic.css'
import '@fontsource/poppins/500-italic.css'
import '@fontsource/poppins/600-italic.css'
import '@fontsource/poppins/700-italic.css'
import '@fontsource/poppins/800-italic.css'
import '@fontsource/poppins/900-italic.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { VueQueryPlugin } from '@tanstack/vue-query'

import App from './App.vue'
import router from './router'
const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(VueQueryPlugin)

app.mount('#app')
