import { fileURLToPath, URL } from 'node:url'
import UnoCSS from 'unocss/vite'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue(), vueDevTools(), UnoCSS()],
  build: {
    outDir: 'build',
    emptyOutDir: true,
    commonjsOptions: {
      include: [/node_modules/, /@tanstack\/vue-query/, /@tanstack\/query-core/],
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  optimizeDeps: {
    esbuildOptions: {
      target: 'es2015', // or 'es5', depending on your target browsers
    },
    include: [
      'node_modules/',
      '@tanstack/vue-query',
      '@tanstack/vue-query-devtools',
      '@tanstack/query-core',
    ],
  },
})
