// unocss.config.ts
import { presetWind } from '@unocss/preset-wind3'
import { defineConfig } from 'unocss'
import presetAnimations from 'unocss-preset-animations'
import { presetShadcn } from 'unocss-preset-shadcn'

export default defineConfig({
  presets: [
    presetWind(),
    presetAnimations(),
    presetShadcn(
      {
        color: 'zinc',
        // With default setting for SolidUI, you need to set the darkSelector option.
        darkSelector: '[data-kb-theme="dark"]',
      },
      {
        // If you are using reka ui.
        componentLibrary: 'reka',
      },
    ),
  ],
  // By default, `.ts` and `.js` files are NOT extracted.
  // If you want to extract them, use the following configuration.
  // It's necessary to add the following configuration if you use shadcn-vue or shadcn-svelte.
  theme: {
    colors: {
      primary: '#F48120',
      secondary: '#0F172A',
      neutral: '#404041',
      background: '#ffffff',
      accent: '#1D46D8',
    },
    fontFamily: {
      rocker: 'New Rocker, sans-serif',
      rocknroll: 'RocknRoll One, sans-serif',
      body: 'Poppins, sans-serif',
    },
  },
  content: {
    pipeline: {
      include: [
        // the default
        /\.(vue|svelte|[jt]sx|mdx?|astro|elm|php|phtml|html|ts)($|\?)/,
        // include js/ts files
        '(components|src)/**/*.{js,ts,jsx,tsx}',
      ],
    },
  },
})
