import {
  require_add_to_unscopables,
  require_array_includes,
  require_export,
  require_fails,
  require_function_uncurry_this,
  require_global_this
} from "./chunk-4SQDRRT4.js";
import {
  __commonJS
} from "./chunk-RR3LOD5A.js";

// node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/modules/es.array.includes.js
var require_es_array_includes = __commonJS({
  "node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/modules/es.array.includes.js"() {
    "use strict";
    var $ = require_export();
    var $includes = require_array_includes().includes;
    var fails = require_fails();
    var addToUnscopables = require_add_to_unscopables();
    var BROKEN_ON_SPARSE = fails(function() {
      return !Array(1).includes();
    });
    $({ target: "Array", proto: true, forced: BROKEN_ON_SPARSE }, {
      includes: function includes(el) {
        return $includes(this, el, arguments.length > 1 ? arguments[1] : void 0);
      }
    });
    addToUnscopables("includes");
  }
});

// node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/internals/entry-unbind.js
var require_entry_unbind = __commonJS({
  "node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/internals/entry-unbind.js"(exports, module) {
    "use strict";
    var globalThis = require_global_this();
    var uncurryThis = require_function_uncurry_this();
    module.exports = function(CONSTRUCTOR, METHOD) {
      return uncurryThis(globalThis[CONSTRUCTOR].prototype[METHOD]);
    };
  }
});

// node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/es/array/includes.js
var require_includes = __commonJS({
  "node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/es/array/includes.js"(exports, module) {
    "use strict";
    require_es_array_includes();
    var entryUnbind = require_entry_unbind();
    module.exports = entryUnbind("Array", "includes");
  }
});

// node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/stable/array/includes.js
var require_includes2 = __commonJS({
  "node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/stable/array/includes.js"(exports, module) {
    "use strict";
    var parent = require_includes();
    module.exports = parent;
  }
});

// node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/actual/array/includes.js
var require_includes3 = __commonJS({
  "node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/actual/array/includes.js"(exports, module) {
    "use strict";
    var parent = require_includes2();
    module.exports = parent;
  }
});

// node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/full/array/includes.js
var require_includes4 = __commonJS({
  "node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/full/array/includes.js"(exports, module) {
    "use strict";
    var parent = require_includes3();
    module.exports = parent;
  }
});

// node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/features/array/includes.js
var require_includes5 = __commonJS({
  "node_modules/.pnpm/core-js@3.44.0/node_modules/core-js/features/array/includes.js"(exports, module) {
    module.exports = require_includes4();
  }
});
export default require_includes5();
//# sourceMappingURL=core-js_features_array_includes.js.map
