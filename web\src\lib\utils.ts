import type { ClassValue } from 'clsx'
import { clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export interface ApiResponse {
  error?: string
  cause?: string
}

export interface HandledError {
  error: string
}

export interface ParsedApiError {
  [key: string]: { message: string }
}

export function handleApiError<T extends ApiResponse>(e: T): HandledError {
  if (e.cause === 'APPLICATION_ERROR' || e.cause === 'JSON_ERROR') {
    return { error: e.error ?? '' }
  }

  if (e.cause === 'VALIDATION_ERROR') {
    const parsedError = JSON.parse(e.error ?? '{}') as ParsedApiError
    const keys = Object.keys(parsedError)

    console.log(parsedError, keys)

    return { error: parsedError[keys[0]].message }
  }

  return { error: e.error ?? '' }
}
